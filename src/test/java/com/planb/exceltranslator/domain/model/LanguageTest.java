package com.planb.exceltranslator.domain.model;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for the Language enum
 */
class LanguageTest {
    
    @Test
    void testLanguageCodes() {
        assertEquals("auto", Language.AUTO_DETECT.getCode());
        assertEquals("en", Language.ENGLISH.getCode());
        assertEquals("ja", Language.JAPANESE.getCode());
        assertEquals("vi", Language.VIETNAMESE.getCode());
    }
    
    @Test
    void testFromCode() {
        assertEquals(Language.ENGLISH, Language.fromCode("en"));
        assertEquals(Language.JAPANESE, Language.fromCode("ja"));
        assertEquals(Language.VIETNAMESE, Language.fromCode("vi"));
        assertEquals(Language.AUTO_DETECT, Language.fromCode("auto"));
    }
    
    @Test
    void testFromCodeCaseInsensitive() {
        assertEquals(Language.ENGLISH, Language.fromCode("EN"));
        assertEquals(Language.JAPANESE, Language.fromCode("JA"));
        assertEquals(Language.VIETNAMESE, Language.fromCode("VI"));
    }
    
    @Test
    void testFromCodeInvalid() {
        assertThrows(IllegalArgumentException.class, () -> Language.fromCode("invalid"));
    }
    
    @Test
    void testGetSourceLanguages() {
        Language[] sourceLanguages = Language.getSourceLanguages();
        assertEquals(4, sourceLanguages.length);
        assertTrue(java.util.Arrays.asList(sourceLanguages).contains(Language.AUTO_DETECT));
    }
    
    @Test
    void testGetTargetLanguages() {
        Language[] targetLanguages = Language.getTargetLanguages();
        assertEquals(3, targetLanguages.length);
        assertFalse(java.util.Arrays.asList(targetLanguages).contains(Language.AUTO_DETECT));
    }
    
    @Test
    void testLocalizedNames() {
        assertEquals("English", Language.ENGLISH.getLocalizedName(Language.ENGLISH));
        assertEquals("英語", Language.ENGLISH.getLocalizedName(Language.JAPANESE));
        assertEquals("Tiếng Anh", Language.ENGLISH.getLocalizedName(Language.VIETNAMESE));
    }
}
