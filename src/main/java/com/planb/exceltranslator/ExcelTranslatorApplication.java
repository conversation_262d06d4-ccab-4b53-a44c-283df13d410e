package com.planb.exceltranslator;

import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.config.HealthChecker;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Locale;
import java.util.MissingResourceException;
import java.util.ResourceBundle;

/**
 * Main JavaFX Application class for Excel Translator
 * Supports multi-language UI and performs startup health checks
 */
public class ExcelTranslatorApplication extends Application {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelTranslatorApplication.class);
    private static final String FXML_FILE = "/fxml/Dashboard.fxml";
    private static final String APP_TITLE = "Excel Translator - PLAN-B";
    
    private ConfigurationManager configManager;
    private LanguageManager languageManager;
    private HealthChecker healthChecker;
    
    @Override
    public void init() throws Exception {
        super.init();
        
        logger.info("Initializing Excel Translator Application...");
        
        // Initialize configuration manager
        configManager = ConfigurationManager.getInstance();
        
        // Initialize language manager with saved language preference
        languageManager = LanguageManager.getInstance();
        String savedLanguage = configManager.getUserPreference("interface.language");
        if (savedLanguage != null && !savedLanguage.isEmpty()) {
            languageManager.setLanguage(savedLanguage);
        }
        
        // Initialize health checker
        healthChecker = new HealthChecker(configManager);
        
        // Perform startup health checks
        performStartupHealthChecks();
        
        logger.info("Application initialization completed successfully");
    }
    
    @Override
    public void start(Stage primaryStage) throws Exception {
        try {
            logger.info("Starting Excel Translator Application UI...");
            
            ResourceBundle bundle = getResourceBundle();
            
            FXMLLoader loader = new FXMLLoader();
            loader.setLocation(getClass().getResource(FXML_FILE));
            loader.setResources(bundle);
            
            Parent root = loader.load();
            Scene scene = new Scene(root);

            primaryStage.titleProperty().bind(languageManager.getStringProperty("app.title"));
            primaryStage.setScene(scene);
            primaryStage.setResizable(false);
            primaryStage.setMinWidth(1014);
            primaryStage.setMinHeight(796);
            
            primaryStage.getIcons().add(new Image(getClass().getResourceAsStream("/icon.png")));
            primaryStage.show();
            
            logger.info("Excel Translator Application UI started successfully");
            
        } catch (IOException e) {
            logger.error("Failed to load FXML file: {}", FXML_FILE, e);
            throw new RuntimeException("Failed to start application", e);
        }
    }
    
    @Override
    public void stop() throws Exception {
        logger.info("Shutting down Excel Translator Application...");
        
        if (configManager != null) {
            configManager.cleanup();
        }
        
        super.stop();
        logger.info("Application shutdown completed");
    }
    
    private void performStartupHealthChecks() {
        logger.info("Performing startup health checks...");
        
        try {
            healthChecker.checkConfiguration();
            logger.info("✓ Configuration check passed");
            
            healthChecker.checkApiCredentials();
            logger.info("✓ API credentials check passed");
            
            healthChecker.checkInternetConnectivity();
            logger.info("✓ Internet connectivity check passed");
            
            logger.info("All health checks passed successfully");
            
        } catch (Exception e) {
            logger.warn("Health check failed: {}", e.getMessage());
        }
    }
    
    private ResourceBundle getResourceBundle() {
        Locale systemLocale = Locale.getDefault();
        String language = systemLocale.getLanguage();
        
        Locale targetLocale;
        switch (language) {
            case "ja":
                targetLocale = Locale.JAPANESE;
                break;
            case "vi":
                targetLocale = new Locale("vi", "VN");
                break;
            default:
                targetLocale = Locale.ENGLISH;
                break;
        }
        
        try {
            return ResourceBundle.getBundle("i18n.messages", targetLocale);
        } catch (MissingResourceException e) {
            logger.warn("Failed to load resource bundle for locale: {}, falling back to English", targetLocale);
            return ResourceBundle.getBundle("i18n.messages", Locale.ENGLISH);
        }
    }
    
    public static void main(String[] args) {
        logger.info("Starting Excel Translator Application...");
        launch(args);
    }
}
