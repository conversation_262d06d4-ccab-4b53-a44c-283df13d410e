package com.planb.exceltranslator.ui.dashboard;

import com.planb.exceltranslator.domain.model.ExcelSheet;
import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ScrollPane;
import javafx.scene.layout.AnchorPane;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * Manages UI state for the Dashboard
 */
public class DashboardUIStateManager {
    
    private static final Logger logger = LoggerFactory.getLogger(DashboardUIStateManager.class);
    
    private StringProperty translateButtonTextProperty;
    private StringProperty statusTextProperty;
    
    public DashboardUIStateManager() {
        translateButtonTextProperty = new SimpleStringProperty();
        statusTextProperty = new SimpleStringProperty();
    }
    
    public StringProperty getTranslateButtonTextProperty() {
        return translateButtonTextProperty;
    }
    
    public StringProperty getStatusTextProperty() {
        return statusTextProperty;
    }
    
    /**
     * Updates the translate button text based on translation state
     */
    public void updateTranslateButtonText(boolean isTranslating, String translateText, String cancelText) {
        if (isTranslating) {
            translateButtonTextProperty.set(cancelText);
        } else {
            translateButtonTextProperty.set(translateText);
        }
    }
    
    /**
     * Updates the status text based on application state
     */
    public void updateStatusText(boolean hasFile, boolean isTranslating, boolean hasResult,
                               String translatingStatus, String completedStatus, 
                               String fileLoadedStatus, String readyStatus) {
        if (isTranslating) {
            statusTextProperty.set(translatingStatus);
        } else if (hasResult) {
            statusTextProperty.set(completedStatus);
        } else if (hasFile) {
            statusTextProperty.set(fileLoadedStatus);
        } else {
            statusTextProperty.set(readyStatus);
        }
    }
    
    /**
     * Updates the sheets display
     */
    public void updateSheetsDisplay(List<ExcelSheet> detectedSheets, AnchorPane sheetsDetectedList, 
                                  ScrollPane sheetsScrollPane) {
        sheetsDetectedList.getChildren().clear();

        if (detectedSheets != null && !detectedSheets.isEmpty()) {
            double yPosition = 15.0;
            final double checkBoxHeight = 30.0;
            final double topPadding = 15.0;
            final double bottomPadding = 15.0;

            for (ExcelSheet sheet : detectedSheets) {
                CheckBox checkBox = new CheckBox(sheet.getDisplayString());
                checkBox.setSelected(sheet.isSelected());
                checkBox.setLayoutX(14.0);
                checkBox.setLayoutY(yPosition);

                checkBox.selectedProperty().addListener((obs, oldVal, newVal) -> sheet.setSelected(newVal));

                sheetsDetectedList.getChildren().add(checkBox);
                yPosition += checkBoxHeight;
            }

            double requiredHeight = topPadding + (detectedSheets.size() * checkBoxHeight) + bottomPadding;
            double minHeight = Math.max(requiredHeight, 128.0);
            
            sheetsDetectedList.setPrefHeight(minHeight);
            sheetsDetectedList.setMinHeight(minHeight);

            Platform.runLater(() -> {
                sheetsDetectedList.autosize();
                if (sheetsScrollPane != null) {
                    sheetsScrollPane.requestLayout();
                }
            });

            logger.debug("Updated sheets display: {} sheets, container height: {}", 
                        detectedSheets.size(), minHeight);
        } else {
            sheetsDetectedList.setPrefHeight(128.0);
            sheetsDetectedList.setMinHeight(128.0);

            Platform.runLater(() -> {
                if (sheetsScrollPane != null) {
                    sheetsScrollPane.requestLayout();
                }
            });
        }
    }
}
