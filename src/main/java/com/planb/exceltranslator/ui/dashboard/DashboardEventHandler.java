package com.planb.exceltranslator.ui.dashboard;

import com.planb.exceltranslator.application.*;
import com.planb.exceltranslator.domain.model.*;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.application.Platform;
import javafx.scene.control.Alert;
import javafx.scene.input.DragEvent;
import javafx.scene.input.Dragboard;
import javafx.scene.input.TransferMode;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.List;
import java.util.function.Consumer;

/**
 * Handles events and user interactions for the Dashboard
 */
public class DashboardEventHandler implements DashboardUIInitializer.DragDropHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(DashboardEventHandler.class);
    
    private final FileAnalysisService fileAnalysisService;
    private final LanguageDetectionService languageDetectionService;
    private final UIService uiService;
    private final LanguageManager languageManager;
    
    private Consumer<File> fileSelectionCallback;
    private Consumer<String> logCallback;
    
    public DashboardEventHandler(FileAnalysisService fileAnalysisService,
                               LanguageDetectionService languageDetectionService,
                               UIService uiService,
                               LanguageManager languageManager) {
        this.fileAnalysisService = fileAnalysisService;
        this.languageDetectionService = languageDetectionService;
        this.uiService = uiService;
        this.languageManager = languageManager;
    }
    
    public void setFileSelectionCallback(Consumer<File> fileSelectionCallback) {
        this.fileSelectionCallback = fileSelectionCallback;
    }
    
    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
    
    /**
     * Handles file selection button click
     */
    public void handleSelectFileButtonClick(Stage ownerStage) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Select Excel File");
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("Excel Files", "*.xlsx", "*.xls"),
                new FileChooser.ExtensionFilter("All Files", "*.*")
        );
        
        File file = fileChooser.showOpenDialog(ownerStage);
        
        if (file != null) {
            selectFile(file);
        }
    }
    
    /**
     * Handles drag over event
     */
    @Override
    public void handleDragOver(DragEvent event) {
        if (event.getGestureSource() != event.getSource() && event.getDragboard().hasFiles()) {
            event.acceptTransferModes(TransferMode.COPY_OR_MOVE);
        }
        event.consume();
    }
    
    /**
     * Handles drag dropped event
     */
    @Override
    public void handleDragDropped(DragEvent event) {
        Dragboard db = event.getDragboard();
        boolean success = false;
        
        if (db.hasFiles()) {
            List<File> files = db.getFiles();
            if (!files.isEmpty()) {
                File file = files.get(0);
                selectFile(file);
                success = true;
            }
        }
        
        event.setDropCompleted(success);
        event.consume();
    }
    
    /**
     * Selects and analyzes an Excel file
     */
    private void selectFile(File file) {
        FileAnalysisService.FileValidationResult validation = fileAnalysisService.validateFile(file);
        if (!validation.isValid()) {
            logMessage(validation.getErrorMessage());
            if (file == null || !file.exists()) {
                showError("error.title.file", "error.message.file_not_exist");
            } else {
                showError("error.title.invalid_file_type", "error.message.invalid_file_type");
            }
            return;
        }

        logMessage("File selected: " + file.getName() + " (" + (file.length() / 1024) + " KB)");
        
        if (fileSelectionCallback != null) {
            fileSelectionCallback.accept(file);
        }
    }
    
    /**
     * Detects and logs the language of the Excel file content
     */
    public void detectAndLogLanguage(List<ExcelSheet> sheets, TranslatorType translatorType,
                                   Consumer<Language> autoDetectedLanguageCallback,
                                   Consumer<Language> targetLanguageUpdateCallback) {
        if (sheets == null || sheets.isEmpty()) {
            return;
        }

        languageDetectionService.setLogCallback(this::logMessage);
        
        languageDetectionService.detectLanguageAsync(sheets, translatorType)
            .thenAccept(result -> Platform.runLater(() -> {
                if (result.isSuccess()) {
                    Language detectedLanguage = result.getDetectedLanguage();
                    logMessage("Detected language: " + detectedLanguage.getEnglishName());

                    if (autoDetectedLanguageCallback != null) {
                        autoDetectedLanguageCallback.accept(detectedLanguage);
                    }
                    
                    if (targetLanguageUpdateCallback != null) {
                        Language newTargetLanguage = uiService.getAlternativeTargetLanguage(detectedLanguage);
                        targetLanguageUpdateCallback.accept(newTargetLanguage);
                    }
                } else {
                    logMessage(result.getMessage());
                }
            }))
            .exceptionally(throwable -> {
                Platform.runLater(() -> {
                    logger.debug("Language detection failed: {}", throwable.getMessage());
                    logMessage("Could not detect language automatically. Please select source language manually.");
                });
                return null;
            });
    }
    
    /**
     * Calculates and sets optimal batch size based on file content
     */
    public void calculateAndSetOptimalBatchSize(List<ExcelSheet> sheets, Consumer<Integer> batchSizeCallback) {
        if (sheets == null || sheets.isEmpty()) {
            return;
        }

        int totalCells = sheets.stream()
                .mapToInt(ExcelSheet::getTranslatableCellCount)
                .sum();

        int optimalBatchSize = uiService.calculateOptimalBatchSize(totalCells);

        if (batchSizeCallback != null) {
            batchSizeCallback.accept(optimalBatchSize);
        }
        
        logMessage("Batch size automatically adjusted to " + optimalBatchSize +
                 " cells per request (optimized for " + totalCells + " total cells)");
    }
    
    private void logMessage(String message) {
        if (logCallback != null) {
            logCallback.accept(message);
        }
    }
    
    private void showError(String titleKey, String messageKey, Object... messageArgs) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(languageManager.getString(titleKey));
        alert.setHeaderText(null);
        
        String message = languageManager.getString(messageKey);
        if (messageArgs.length > 0) {
            message = java.text.MessageFormat.format(message, messageArgs);
        }
        alert.setContentText(message);
        alert.showAndWait();
    }
}
