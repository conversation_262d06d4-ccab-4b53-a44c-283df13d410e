package com.planb.exceltranslator.ui.dashboard;

import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.scene.control.TextArea;
import javafx.util.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Manages logging functionality for the Dashboard UI
 */
public class DashboardLogManager {
    
    private static final Logger logger = LoggerFactory.getLogger(DashboardLogManager.class);
    private static final int MAX_LOG_LINES = 500;
    
    private TextArea logArea;
    private boolean userIsScrollingManually = false;
    private Timeline autoScrollResetTimer;
    
    public DashboardLogManager(TextArea logArea) {
        this.logArea = logArea;
        initializeLogging();
    }
    
    /**
     * Initializes the logging display
     */
    private void initializeLogging() {
        if (logArea == null) {
            logger.error("logArea TextArea is null - FXML injection failed");
            return;
        }

        logArea.clear();
        logArea.setWrapText(false);
        logArea.setEditable(false);
        logArea.setFocusTraversable(true);
        
        setupKeyNavigation();
        initializeAutoScrollBehavior();

        addLogMessage("========== APPLICATION INITIALIZED ==========");
        addLogMessage("Excel Translator initialized - Ready to process files");
        addLogMessage("Drag and drop an Excel file or click 'Select File' to begin");

        logger.debug("Logging display initialized");
    }
    
    private void setupKeyNavigation() {
        logArea.setOnKeyPressed(event -> {
            switch (event.getCode()) {
                case HOME:
                    logArea.home();
                    event.consume();
                    break;
                case END:
                    logArea.end();
                    event.consume();
                    break;
                default:
                    break;
            }
        });
    }
    
    /**
     * Adds a log message to the UI with proper formatting and auto-scroll
     */
    public void addLogMessage(String message) {
        addLogMessage(message, LogLevel.INFO);
    }

    /**
     * Adds a log message with specific log level
     */
    public void addLogMessage(String message, LogLevel level) {
        if (message == null || message.trim().isEmpty()) {
            return;
        }

        if (!Platform.isFxApplicationThread()) {
            Platform.runLater(() -> addLogMessage(message, level));
            return;
        }

        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            String formattedMessage = String.format("[%s] %s %s", timestamp, level.getPrefix(), message);
            
            appendToLogArea(formattedMessage);
            autoScrollToBottom();
            
            logger.info("UI Log [{}]: {}", level.getPrefix(), message);

        } catch (Exception e) {
            logger.error("Failed to add log message to UI: {}", message, e);
        }
    }
    
    private void appendToLogArea(String formattedMessage) {
        String currentText = logArea.getText();
        String newText = currentText + formattedMessage + "\n";

        String[] lines = newText.split("\n");
        if (lines.length > MAX_LOG_LINES) {
            int linesToKeep = MAX_LOG_LINES;
            StringBuilder trimmedText = new StringBuilder();
            for (int i = lines.length - linesToKeep; i < lines.length; i++) {
                trimmedText.append(lines[i]).append("\n");
            }
            newText = trimmedText.toString();
        }

        logArea.setText(newText);
    }
    
    private void autoScrollToBottom() {
        Platform.runLater(() -> {
            Platform.runLater(() -> {
                if (!userIsScrollingManually && logArea != null) {
                    logArea.setScrollTop(Double.MAX_VALUE);
                }
            });
        });
    }
    
    /**
     * Initialize intelligent auto-scroll behavior
     */
    private void initializeAutoScrollBehavior() {
        if (logArea == null) return;

        logArea.setOnMouseClicked(e -> {
            userIsScrollingManually = true;
            resetAutoScrollTimer();
        });

        logArea.setOnKeyPressed(e -> {
            switch (e.getCode()) {
                case UP:
                case DOWN:
                case PAGE_UP:
                case PAGE_DOWN:
                case HOME:
                case END:
                    userIsScrollingManually = true;
                    resetAutoScrollTimer();
                    break;
                default:
                    break;
            }
        });
    }

    /**
     * Reset auto-scroll timer to resume auto-scrolling after user inactivity
     */
    private void resetAutoScrollTimer() {
        if (autoScrollResetTimer != null) {
            autoScrollResetTimer.stop();
        }

        autoScrollResetTimer = new Timeline(new KeyFrame(Duration.seconds(3), e -> {
            userIsScrollingManually = false;
            Platform.runLater(() -> {
                if (!userIsScrollingManually && logArea != null) {
                    logArea.setScrollTop(Double.MAX_VALUE);
                }
            });
        }));
        autoScrollResetTimer.play();
    }
    
    /**
     * Log levels for proper categorization
     */
    public enum LogLevel {
        INFO("INFO:", "#FFFFFF"),
        WARNING("WARNING:", "#FFD93D"),
        ERROR("ERROR:", "#FF6B6B");

        private final String prefix;
        private final String color;

        LogLevel(String prefix, String color) {
            this.prefix = prefix;
            this.color = color;
        }

        public String getPrefix() { return prefix; }
        public String getColor() { return color; }
    }
}
