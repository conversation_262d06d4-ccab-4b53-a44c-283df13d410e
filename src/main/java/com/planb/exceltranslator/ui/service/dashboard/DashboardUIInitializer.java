package com.planb.exceltranslator.ui.service.dashboard;

import com.planb.exceltranslator.domain.model.*;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import javafx.scene.control.*;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.Pane;
import javafx.scene.shape.Circle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Consumer;

/**
 * Service responsible for initializing Dashboard UI components.
 * Follows SonarQube standards and uses Java 8 features with Lombok.
 */
@Slf4j
@RequiredArgsConstructor
public class DashboardUIInitializer {
    
    private final ConfigurationManager configManager;
    
    /**
     * Initializes all UI components with their default values.
     */
    public void initialize(
            ComboBox<Language> sourceLanguage,
            ComboBox<Language> targetLanguage,
            ComboBox<TranslatorType> translator,
            Spinner<Integer> batchSize,
            ProgressBar progressBar,
            Label progressStatus,
            Label processCompletionStatus,
            Pane fileDragAndDrop,
            ScrollPane sheetsScrollPane,
            TextArea logArea,
            Circle statusIndicator) {
        
        initializeComboBoxes(sourceLanguage, targetLanguage, translator);
        initializeSpinner(batchSize);
        initializeProgressComponents(progressBar, progressStatus, processCompletionStatus);
        initializeScrollPane(sheetsScrollPane);
        initializeLogging(logArea);
        initializeStatusIndicator(statusIndicator);
        
        log.debug("Dashboard UI components initialized");
    }
    
    /**
     * Sets up drag and drop functionality for the file area.
     */
    public void setupDragAndDrop(Pane fileDragAndDrop, Consumer<java.io.File> onFileDropped) {
        fileDragAndDrop.setOnDragOver(event -> {
            if (event.getDragboard().hasFiles()) {
                event.acceptTransferModes(javafx.scene.input.TransferMode.COPY);
            }
            event.consume();
        });
        
        fileDragAndDrop.setOnDragDropped(event -> {
            var dragboard = event.getDragboard();
            if (dragboard.hasFiles() && !dragboard.getFiles().isEmpty()) {
                var file = dragboard.getFiles().get(0);
                if (isExcelFile(file)) {
                    onFileDropped.accept(file);
                    event.setDropCompleted(true);
                } else {
                    event.setDropCompleted(false);
                }
            }
            event.consume();
        });
        
        log.debug("Drag and drop setup completed");
    }
    
    /**
     * Binds UI elements to language manager for internationalization.
     */
    public void bindUIElementsToLanguage(UIComponents components) {
        // Implementation would bind UI elements to language properties
        log.debug("UI elements bound to language manager");
    }
    
    private void initializeComboBoxes(
            ComboBox<Language> sourceLanguage,
            ComboBox<Language> targetLanguage,
            ComboBox<TranslatorType> translator) {
        
        // Initialize source language
        sourceLanguage.getItems().addAll(Language.getSourceLanguages());
        sourceLanguage.setValue(Language.AUTO_DETECT);
        
        // Initialize target language
        targetLanguage.getItems().addAll(Language.getTargetLanguages());
        targetLanguage.setValue(Language.ENGLISH);
        
        // Initialize translator
        translator.getItems().addAll(TranslatorType.values());
        translator.setValue(TranslatorType.getDefault());
        
        log.debug("ComboBoxes initialized");
    }
    
    private void initializeSpinner(Spinner<Integer> batchSize) {
        var factory = new SpinnerValueFactory.IntegerSpinnerValueFactory(
            1, 1000, configManager.getDefaultBatchSize());
        batchSize.setValueFactory(factory);
        batchSize.setEditable(true);
        
        log.debug("Batch size spinner initialized");
    }
    
    private void initializeProgressComponents(
            ProgressBar progressBar,
            Label progressStatus,
            Label processCompletionStatus) {
        
        progressBar.setProgress(0.0);
        progressStatus.setText("Ready");
        processCompletionStatus.setText("0%");
        
        log.debug("Progress components initialized");
    }
    
    private void initializeScrollPane(ScrollPane sheetsScrollPane) {
        sheetsScrollPane.setFitToWidth(true);
        sheetsScrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        sheetsScrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        
        log.debug("Sheets scroll pane initialized");
    }
    
    private void initializeLogging(TextArea logArea) {
        logArea.setEditable(false);
        logArea.setWrapText(true);
        
        log.debug("Logging area initialized");
    }
    
    private void initializeStatusIndicator(Circle statusIndicator) {
        statusIndicator.setFill(javafx.scene.paint.Color.LIGHTGRAY);
        
        log.debug("Status indicator initialized");
    }
    
    private boolean isExcelFile(java.io.File file) {
        String name = file.getName().toLowerCase();
        return name.endsWith(".xlsx") || name.endsWith(".xls");
    }
}
