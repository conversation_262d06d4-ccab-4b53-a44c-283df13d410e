package com.planb.exceltranslator.ui.service.dashboard;

import javafx.scene.control.Label;
import javafx.scene.control.ProgressBar;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;
import lombok.extern.slf4j.Slf4j;

/**
 * Service responsible for handling progress updates in the Dashboard.
 * Follows SonarQube standards and uses Java 8 features with Lombok.
 */
@Slf4j
public class DashboardProgressHandler {
    
    private ProgressBar progressBar;
    private Label progressStatus;
    private Label processCompletionStatus;
    private Circle statusIndicator;
    
    /**
     * Initializes the progress handler with UI components.
     */
    public void initialize(
            ProgressBar progressBar,
            Label progressStatus,
            Label processCompletionStatus,
            Circle statusIndicator) {
        
        this.progressBar = progressBar;
        this.progressStatus = progressStatus;
        this.processCompletionStatus = processCompletionStatus;
        this.statusIndicator = statusIndicator;
        
        resetProgress();
        
        log.debug("DashboardProgressHandler initialized");
    }
    
    /**
     * Updates progress with percentage and message.
     */
    public void updateProgress(double progress, String message) {
        if (progressBar != null) {
            progressBar.setProgress(progress);
        }
        
        if (progressStatus != null) {
            progressStatus.setText(message);
        }
        
        if (processCompletionStatus != null) {
            int percentage = (int) (progress * 100);
            processCompletionStatus.setText(percentage + "%");
        }
        
        updateStatusIndicatorForProgress(progress);
        
        log.debug("Progress updated: {}% - {}", (int) (progress * 100), message);
    }
    
    /**
     * Updates UI state based on translation status.
     */
    public void updateUIState(boolean isTranslating) {
        if (isTranslating) {
            if (progressStatus != null) {
                progressStatus.setText("Translation in progress...");
            }
            updateStatusIndicator("processing");
        } else {
            resetProgress();
            updateStatusIndicator("ready");
        }
    }
    
    /**
     * Resets progress to initial state.
     */
    public void resetProgress() {
        if (progressBar != null) {
            progressBar.setProgress(0.0);
        }
        
        if (progressStatus != null) {
            progressStatus.setText("Ready");
        }
        
        if (processCompletionStatus != null) {
            processCompletionStatus.setText("0%");
        }
        
        updateStatusIndicator("ready");
    }
    
    /**
     * Updates status indicator based on current state.
     */
    public void updateStatusIndicator(String status) {
        if (statusIndicator == null) {
            return;
        }
        
        switch (status.toLowerCase()) {
            case "ready":
                statusIndicator.setFill(Color.LIGHTGRAY);
                break;
            case "processing":
                statusIndicator.setFill(Color.ORANGE);
                break;
            case "success":
                statusIndicator.setFill(Color.GREEN);
                break;
            case "error":
                statusIndicator.setFill(Color.RED);
                break;
            case "warning":
                statusIndicator.setFill(Color.YELLOW);
                break;
            default:
                statusIndicator.setFill(Color.LIGHTGRAY);
                break;
        }
    }
    
    private void updateStatusIndicatorForProgress(double progress) {
        if (progress >= 1.0) {
            updateStatusIndicator("success");
        } else if (progress > 0.0) {
            updateStatusIndicator("processing");
        } else {
            updateStatusIndicator("ready");
        }
    }
}
