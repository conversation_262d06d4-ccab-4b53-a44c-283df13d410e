package com.planb.exceltranslator.ui.service.dashboard;

import javafx.scene.control.*;
import javafx.scene.layout.AnchorPane;
import lombok.Builder;
import lombok.Getter;

/**
 * Data class holding UI components for binding operations.
 * Uses Lombok for cleaner code.
 */
@Getter
@Builder
public class UIComponents {
    private final Label selectedFile;
    private final Button selectFileButton;
    private final Label fileSize;
    private final ComboBox<?> sourceLanguage;
    private final ComboBox<?> targetLanguage;
    private final ComboBox<?> translator;
    private final Button translateButton;
    private final Button cancelButton;
    private final Button exportButton;
    private final AnchorPane sheetsDetectedList;
    private final TextArea logArea;
}
