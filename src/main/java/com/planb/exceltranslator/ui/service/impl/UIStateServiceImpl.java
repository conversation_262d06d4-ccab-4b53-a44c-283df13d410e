package com.planb.exceltranslator.ui.service.impl;

import com.planb.exceltranslator.ui.service.UIStateService;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.scene.control.Button;

/**
 * Implementation of UIStateService
 */
public class UIStateServiceImpl implements UIStateService {
    
    private final Button translateButton;
    private final Button exportButton;
    private final LanguageManager languageManager;
    
    public UIStateServiceImpl(Button translateButton, Button exportButton, LanguageManager languageManager) {
        this.translateButton = translateButton;
        this.exportButton = exportButton;
        this.languageManager = languageManager;
    }
    
    @Override
    public void enableTranslation() {
        translateButton.setDisable(false);
    }
    
    @Override
    public void disableTranslation() {
        translateButton.setDisable(true);
    }
    
    @Override
    public void enableExport() {
        exportButton.setDisable(false);
    }
    
    @Override
    public void disableExport() {
        exportButton.setDisable(true);
    }
    
    @Override
    public void updateLanguageBindings() {
        // Update button text based on current language
        // This would typically use the LanguageManager to get localized strings
    }
}
