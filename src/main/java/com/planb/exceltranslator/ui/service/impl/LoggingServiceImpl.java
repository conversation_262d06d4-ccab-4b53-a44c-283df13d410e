package com.planb.exceltranslator.ui.service.impl;

import com.planb.exceltranslator.ui.service.LoggingService;
import javafx.scene.control.TextArea;
import javafx.scene.shape.Circle;

/**
 * Implementation of LoggingService
 */
public class LoggingServiceImpl extends LoggingService {
    
    private final Circle statusIndicator;
    
    public LoggingServiceImpl(TextArea logArea, Circle statusIndicator) {
        super(logArea);
        this.statusIndicator = statusIndicator;
    }
    
    // Add any additional implementation-specific methods here
}
