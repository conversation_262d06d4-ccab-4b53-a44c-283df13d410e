package com.planb.exceltranslator.ui.service;

import lombok.Builder;
import lombok.Getter;
import com.planb.exceltranslator.ui.component.UIControlsInitializer;

/**
 * Container for all UI services
 */
@Getter
@Builder
public class UIServices {
    private final LoggingService loggingService;
    private final ProgressService progressService;
    private final SheetsDisplayService sheetsDisplayService;
    private final UIStateService uiStateService;
    private final DialogService dialogService;
    private final UIControlsInitializer controlsInitializer;
    private final FileOperationService fileOperationService;
    private final DashboardBusinessService businessService;
}
