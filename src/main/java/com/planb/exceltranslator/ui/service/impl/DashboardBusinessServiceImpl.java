package com.planb.exceltranslator.ui.service.impl;

import com.planb.exceltranslator.ui.service.DashboardBusinessService;
import com.planb.exceltranslator.ui.service.FileOperationService;
import com.planb.exceltranslator.ui.service.LoggingService;
import com.planb.exceltranslator.ui.service.SheetsDisplayService;
import com.planb.exceltranslator.ui.service.UIStateService;
import com.planb.exceltranslator.ui.service.DialogService;

/**
 * Implementation of DashboardBusinessService
 */
public class DashboardBusinessServiceImpl implements DashboardBusinessService {
    
    private final FileOperationService fileOperationService;
    private final LoggingService loggingService;
    private final SheetsDisplayService sheetsDisplayService;
    private final UIStateService uiStateService;
    private final DialogService dialogService;
    
    public DashboardBusinessServiceImpl(
            FileOperationService fileOperationService,
            LoggingService loggingService,
            SheetsDisplayService sheetsDisplayService,
            UIStateService uiStateService,
            DialogService dialogService) {
        this.fileOperationService = fileOperationService;
        this.loggingService = loggingService;
        this.sheetsDisplayService = sheetsDisplayService;
        this.uiStateService = uiStateService;
        this.dialogService = dialogService;
    }
    
    @Override
    public void handleFileSelection() {
        fileOperationService.selectFile();
    }
    
    @Override
    public void handleTranslation() {
        // Implementation for translation handling
    }
    
    @Override
    public void handleExport() {
        fileOperationService.exportFile();
    }
    
    @Override
    public void initialize() {
        // Implementation for initialization
    }
}
