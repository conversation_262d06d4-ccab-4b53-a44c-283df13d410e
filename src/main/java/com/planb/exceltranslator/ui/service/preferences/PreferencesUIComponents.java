package com.planb.exceltranslator.ui.service.preferences;

import javafx.scene.control.*;
import lombok.Builder;
import lombok.Getter;

/**
 * Data class holding Preferences UI components for binding operations.
 * Uses Lombok for cleaner code.
 */
@Getter
@Builder
public class PreferencesUIComponents {
    private final Tab interfaceTab;
    private final Tab apiKeysTab;
    private final Label interfaceLanguageLabel;
    private final Label languageNoteLabel;
    private final Label deeplKeyLabel;
    private final Label deeplInfoLabel;
    private final Label googleKeyLabel;
    private final Label googleInfoLabel;
    private final Label securityNoteLabel;
    private final Button saveButton;
    private final Button cancelButton;
    private final ComboBox<String> interfaceLanguageComboBox;
    private final PasswordField deeplApiKeyField;
    private final PasswordField googleApiKeyField;
}
