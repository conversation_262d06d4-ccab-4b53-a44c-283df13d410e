package com.planb.exceltranslator.ui.service.dashboard;

import com.planb.exceltranslator.application.FileAnalysisService;
import com.planb.exceltranslator.domain.model.ExcelSheet;
import com.planb.exceltranslator.domain.model.Language;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * Service responsible for handling file operations in the Dashboard.
 * Follows SonarQube standards and uses Java 8 features with Lombok.
 */
@Slf4j
@RequiredArgsConstructor
public class DashboardFileHandler {
    
    private final FileAnalysisService fileAnalysisService;
    
    private Consumer<File> onFileSelectedCallback;
    private BiConsumer<List<ExcelSheet>, Language> onFileAnalyzedCallback;
    
    /**
     * Initializes the file handler with callbacks.
     */
    public void initialize(
            Consumer<File> onFileSelected,
            BiConsumer<List<ExcelSheet>, Language> onFileAnalyzed) {
        this.onFileSelectedCallback = onFileSelected;
        this.onFileAnalyzedCallback = onFileAnalyzed;
        
        log.debug("DashboardFileHandler initialized");
    }
    
    /**
     * Opens file selection dialog and returns selected file.
     */
    public CompletableFuture<File> selectFile() {
        return CompletableFuture.supplyAsync(() -> {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Select Excel File");
            fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("Excel Files", "*.xlsx", "*.xls"),
                new FileChooser.ExtensionFilter("All Files", "*.*")
            );
            
            Stage stage = new Stage(); // In real implementation, get the current stage
            File selectedFile = fileChooser.showOpenDialog(stage);
            
            if (selectedFile != null && onFileSelectedCallback != null) {
                onFileSelectedCallback.accept(selectedFile);
            }
            
            return selectedFile;
        });
    }
    
    /**
     * Analyzes the given file asynchronously.
     */
    public CompletableFuture<FileAnalysisResult> analyzeFileAsync(File file) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("Starting analysis of file: {}", file.getName());
                
                var analysisResult = fileAnalysisService.analyzeFile(file);
                
                if (analysisResult.isSuccess()) {
                    var sheets = analysisResult.getSheets();
                    var detectedLanguage = detectLanguageFromSheets(sheets);
                    
                    if (onFileAnalyzedCallback != null) {
                        onFileAnalyzedCallback.accept(sheets, detectedLanguage);
                    }
                    
                    return FileAnalysisResult.builder()
                        .success(true)
                        .sheets(sheets)
                        .detectedLanguage(detectedLanguage)
                        .build();
                } else {
                    return FileAnalysisResult.builder()
                        .success(false)
                        .errorMessage(analysisResult.getErrorMessage())
                        .build();
                }
                
            } catch (Exception e) {
                log.error("File analysis failed", e);
                return FileAnalysisResult.builder()
                    .success(false)
                    .errorMessage(e.getMessage())
                    .build();
            }
        });
    }
    
    /**
     * Opens file selection dialog for output file.
     */
    public CompletableFuture<File> selectOutputFile(File inputFile) {
        return CompletableFuture.supplyAsync(() -> {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Save Translated File");
            fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("Excel Files", "*.xlsx", "*.xls")
            );
            
            // Suggest a filename
            if (inputFile != null) {
                String originalName = inputFile.getName();
                String extension = originalName.substring(originalName.lastIndexOf('.'));
                String baseName = originalName.substring(0, originalName.lastIndexOf('.'));
                String suggestedName = baseName + "_translated" + extension;
                fileChooser.setInitialFileName(suggestedName);
            }
            
            Stage stage = new Stage(); // In real implementation, get the current stage
            return fileChooser.showSaveDialog(stage);
        });
    }
    
    private Language detectLanguageFromSheets(List<ExcelSheet> sheets) {
        // Placeholder for language detection logic
        // In real implementation, this would analyze the content of sheets
        // and use a language detection service
        return Language.AUTO_DETECT;
    }
}
