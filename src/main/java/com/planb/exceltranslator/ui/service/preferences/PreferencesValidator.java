package com.planb.exceltranslator.ui.service.preferences;

import javafx.scene.control.Label;
import javafx.scene.control.PasswordField;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Pattern;

/**
 * Service responsible for validating API keys in the Preferences dialog.
 * Follows SonarQube standards and uses Java 8 features with Lombok.
 */
@Slf4j
public class PreferencesValidator {
    
    private static final Pattern DEEPL_KEY_PATTERN = Pattern.compile("^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}:fx$");
    private static final Pattern GOOGLE_KEY_PATTERN = Pattern.compile("^[A-Za-z0-9_-]{39}$");
    
    /**
     * Sets up API key validation for the given fields.
     */
    public void setupApiKeyValidation(
            PasswordField deeplApiKeyField,
            PasswordField googleApiKeyField,
            Label deeplApiStatusIndicator,
            Label googleApiStatusIndicator,
            PreferencesUIManager uiManager) {
        
        // Setup real-time validation
        deeplApiKeyField.textProperty().addListener((obs, oldVal, newVal) -> 
            validateDeeplApiKey(newVal, deeplApiStatusIndicator));
        
        googleApiKeyField.textProperty().addListener((obs, oldVal, newVal) -> 
            validateGoogleApiKey(newVal, googleApiStatusIndicator));
        
        log.debug("API key validation setup completed");
    }
    
    /**
     * Validates DeepL API key format.
     */
    public void validateDeeplApiKey(String apiKey, Label statusIndicator) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            updateStatusIndicator(statusIndicator, "Not configured", "gray");
            return;
        }
        
        if (DEEPL_KEY_PATTERN.matcher(apiKey.trim()).matches()) {
            updateStatusIndicator(statusIndicator, "✓ Valid format", "green");
        } else {
            updateStatusIndicator(statusIndicator, "✗ Invalid format", "red");
        }
    }
    
    /**
     * Validates Google API key format.
     */
    public void validateGoogleApiKey(String apiKey, Label statusIndicator) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            updateStatusIndicator(statusIndicator, "Not configured", "gray");
            return;
        }
        
        if (GOOGLE_KEY_PATTERN.matcher(apiKey.trim()).matches()) {
            updateStatusIndicator(statusIndicator, "✓ Valid format", "green");
        } else {
            updateStatusIndicator(statusIndicator, "✗ Invalid format", "red");
        }
    }
    
    private void updateStatusIndicator(Label indicator, String text, String color) {
        indicator.setText(text);
        indicator.setStyle("-fx-text-fill: " + color + ";");
    }
}
