package com.planb.exceltranslator.ui.service.dashboard;

import com.planb.exceltranslator.domain.model.Language;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.scene.control.ComboBox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.function.BiConsumer;

/**
 * Service responsible for handling language-related operations in the Dashboard.
 * Follows SonarQube standards and uses Java 8 features with Lombok.
 */
@Slf4j
@RequiredArgsConstructor
public class DashboardLanguageHandler {
    
    private final LanguageManager languageManager;
    
    private BiConsumer<Language, Language> languageChangeCallback;
    
    /**
     * Initializes the language handler with callback.
     */
    public void initialize(BiConsumer<Language, Language> onLanguageChanged) {
        this.languageChangeCallback = onLanguageChanged;
        log.debug("DashboardLanguageHandler initialized");
    }
    
    /**
     * Sets up language bindings for source and target language combo boxes.
     */
    public void setupLanguageBindings(
            ComboBox<Language> sourceLanguage,
            ComboBox<Language> targetLanguage) {
        
        sourceLanguage.valueProperty().addListener((obs, oldVal, newVal) -> {
            if (oldVal != newVal && languageChangeCallback != null) {
                languageChangeCallback.accept(newVal, targetLanguage.getValue());
            }
        });
        
        targetLanguage.valueProperty().addListener((obs, oldVal, newVal) -> {
            if (oldVal != newVal && languageChangeCallback != null) {
                languageChangeCallback.accept(sourceLanguage.getValue(), newVal);
            }
        });
        
        log.debug("Language bindings setup completed");
    }
    
    /**
     * Handles language change events and suggests alternatives if needed.
     */
    public void handleLanguageChange(
            Language source,
            Language target,
            ComboBox<Language> targetLanguageCombo) {
        
        if (source != null && target != null && source.equals(target)) {
            Language suggested = suggestAlternativeTarget(source);
            if (suggested != null) {
                targetLanguageCombo.setValue(suggested);
                log.info("Auto-suggested target language: {}", suggested.getDisplayName());
            }
        }
    }
    
    /**
     * Suggests an alternative target language when source and target are the same.
     */
    public Language suggestAlternativeTarget(Language sourceLanguage) {
        if (sourceLanguage == null) {
            return Language.ENGLISH;
        }
        
        // If source is English, suggest a different language
        if (sourceLanguage == Language.ENGLISH) {
            return Language.JAPANESE; // or any other default
        }
        
        // For all other languages, suggest English
        return Language.ENGLISH;
    }
}
