package com.planb.exceltranslator.ui.service.impl;

import com.planb.exceltranslator.ui.service.ProgressService;
import javafx.scene.control.Label;
import javafx.scene.control.ProgressBar;

/**
 * Implementation of ProgressService
 */
public class ProgressServiceImpl extends ProgressService {
    
    public ProgressServiceImpl(ProgressBar progressBar, Label progressStatus, Label processCompletionStatus) {
        super();
        // Initialize the inherited fields using the parent's initialize method
        initialize(progressBar, progressStatus, processCompletionStatus, null);
    }
    
    // Add any additional implementation-specific methods here
}
