package com.planb.exceltranslator.ui.service.preferences;

import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.scene.control.*;
import javafx.stage.Stage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service responsible for managing UI elements in the Preferences dialog.
 * Follows SonarQube standards and uses Java 8 features with Lombok.
 */
@Slf4j
@RequiredArgsConstructor
public class PreferencesUIManager {
    
    private final ConfigurationManager configManager;
    private final LanguageManager languageManager;
    
    /**
     * Loads current settings into UI components.
     */
    public void loadCurrentSettings(
            ComboBox<String> interfaceLanguageComboBox,
            PasswordField deeplApiKeyField,
            PasswordField googleApiKeyField,
            Label deeplApiStatusIndicator,
            Label googleApiStatusIndicator) {
        
        // Load language setting
        String currentLanguage = configManager.getUserPreference("interface.language");
        String displayLanguage = mapLanguageCodeToDisplay(currentLanguage);
        interfaceLanguageComboBox.setValue(displayLanguage);
        
        // Load API keys (masked)
        String deeplKey = configManager.getUserPreference("deepl.api.key");
        if (deeplKey != null && !deeplKey.isEmpty()) {
            deeplApiKeyField.setPromptText("API key configured");
            deeplApiStatusIndicator.setText("✓ Configured");
            deeplApiStatusIndicator.setStyle("-fx-text-fill: green;");
        }
        
        String googleKey = configManager.getUserPreference("google.api.key");
        if (googleKey != null && !googleKey.isEmpty()) {
            googleApiKeyField.setPromptText("API key configured");
            googleApiStatusIndicator.setText("✓ Configured");
            googleApiStatusIndicator.setStyle("-fx-text-fill: green;");
        }
        
        log.debug("Current settings loaded into UI");
    }
    
    /**
     * Binds UI elements to language manager for internationalization.
     */
    public void bindUIElementsToLanguage(PreferencesUIComponents components) {
        try {
            // Bind tabs
            components.getInterfaceTab().textProperty().bind(
                languageManager.getStringProperty("preferences.tab.interface"));
            components.getApiKeysTab().textProperty().bind(
                languageManager.getStringProperty("preferences.tab.apikeys"));
            
            // Bind labels
            components.getInterfaceLanguageLabel().textProperty().bind(
                languageManager.getStringProperty("preferences.language.interface"));
            components.getLanguageNoteLabel().textProperty().bind(
                languageManager.getStringProperty("preferences.language.note"));
            
            components.getDeeplKeyLabel().textProperty().bind(
                languageManager.getStringProperty("preferences.deepl.key"));
            components.getDeeplInfoLabel().textProperty().bind(
                languageManager.getStringProperty("preferences.deepl.info"));
            
            components.getGoogleKeyLabel().textProperty().bind(
                languageManager.getStringProperty("preferences.google.key"));
            components.getGoogleInfoLabel().textProperty().bind(
                languageManager.getStringProperty("preferences.google.info"));
            
            components.getSecurityNoteLabel().textProperty().bind(
                languageManager.getStringProperty("preferences.security.note"));
            
            // Bind buttons
            components.getSaveButton().textProperty().bind(
                languageManager.getStringProperty("button.save"));
            components.getCancelButton().textProperty().bind(
                languageManager.getStringProperty("button.cancel"));
            
            log.debug("UI elements bound to language manager");
            
        } catch (Exception e) {
            log.error("Failed to bind UI elements to language manager", e);
        }
    }
    
    /**
     * Binds stage title to language manager.
     */
    public void bindStageToLanguage(Stage stage) {
        stage.titleProperty().bind(languageManager.getStringProperty("preferences.title"));
    }
    
    private String mapLanguageCodeToDisplay(String languageCode) {
        if (languageCode == null) {
            return "English (Default)";
        }
        
        switch (languageCode) {
            case "ja":
                return "日本語 (Japanese)";
            case "vi":
                return "Tiếng Việt (Vietnamese)";
            default:
                return "English (Default)";
        }
    }
}
