package com.planb.exceltranslator.ui.service;

import javafx.application.Platform;
import javafx.scene.control.Label;
import javafx.scene.control.ProgressBar;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Service responsible for managing progress tracking and status display.
 * Handles progress bar updates, status indicators, and completion status.
 */
public class ProgressService {
    
    private static final Logger logger = LoggerFactory.getLogger(ProgressService.class);
    
    private ProgressBar progressBar;
    private Label progressStatus;
    private Label processCompletionStatus;
    private Circle statusIndicator;
    
    private double currentProgress = 0.0;
    private String currentStatus = "";
    private ProgressState currentState = ProgressState.IDLE;
    
    public ProgressService() {
        logger.debug("ProgressService created");
    }
    
    /**
     * Initializes the progress tracking service with UI components
     */
    public void initialize(ProgressBar progressBar, 
                          Label progressStatus, 
                          Label processCompletionStatus, 
                          Circle statusIndicator) {
        this.progressBar = progressBar;
        this.progressStatus = progressStatus;
        this.processCompletionStatus = processCompletionStatus;
        this.statusIndicator = statusIndicator;
        
        reset();
        logger.debug("ProgressTrackingService initialized with UI components");
    }
    
    /**
     * Updates the progress with percentage and message
     * 
     * @param percent Progress percentage (0-100)
     * @param message Status message
     */
    public void updateProgress(int percent, String message) {
        updateProgress(percent / 100.0, message);
    }
    
    /**
     * Updates the progress with decimal value and message
     * 
     * @param progress Progress value (0.0-1.0)
     * @param message Status message
     */
    public void updateProgress(double progress, String message) {
        currentProgress = Math.max(0.0, Math.min(1.0, progress));
        currentStatus = message != null ? message : "";
        
        Platform.runLater(() -> {
            if (progressBar != null) {
                progressBar.setProgress(currentProgress);
                progressBar.setVisible(currentProgress > 0.0);
            }
            
            if (progressStatus != null) {
                progressStatus.setText(currentStatus);
            }
            
            updateStatusIndicator(ProgressState.IN_PROGRESS);
        });
        
        logger.debug("Progress updated: {}% - {}", (int)(currentProgress * 100), currentStatus);
    }
    
    /**
     * Sets the progress state to indicate completion
     * 
     * @param success Whether the operation completed successfully
     * @param message Completion message
     */
    public void setCompleted(boolean success, String message) {
        currentProgress = success ? 1.0 : 0.0;
        currentStatus = message != null ? message : "";
        currentState = success ? ProgressState.SUCCESS : ProgressState.ERROR;
        
        Platform.runLater(() -> {
            if (progressBar != null) {
                progressBar.setProgress(currentProgress);
                progressBar.setVisible(true);
            }
            
            if (progressStatus != null) {
                progressStatus.setText(currentStatus);
            }
            
            if (processCompletionStatus != null) {
                processCompletionStatus.setText(success ? "Completed" : "Failed");
            }
            
            updateStatusIndicator(currentState);
        });
        
        logger.info("Progress completed: {} - {}", success ? "SUCCESS" : "FAILED", currentStatus);
    }
    
    /**
     * Sets the progress state to indicate an error
     * 
     * @param errorMessage Error message to display
     */
    public void setError(String errorMessage) {
        setCompleted(false, errorMessage);
    }
    
    /**
     * Sets the progress state to indicate cancellation
     * 
     * @param message Cancellation message
     */
    public void setCancelled(String message) {
        currentProgress = 0.0;
        currentStatus = message != null ? message : "Cancelled";
        currentState = ProgressState.CANCELLED;
        
        Platform.runLater(() -> {
            if (progressBar != null) {
                progressBar.setProgress(0.0);
                progressBar.setVisible(false);
            }
            
            if (progressStatus != null) {
                progressStatus.setText(currentStatus);
            }
            
            if (processCompletionStatus != null) {
                processCompletionStatus.setText("Cancelled");
            }
            
            updateStatusIndicator(currentState);
        });
        
        logger.info("Progress cancelled: {}", currentStatus);
    }
    
    /**
     * Resets the progress tracking to initial state
     */
    public void reset() {
        currentProgress = 0.0;
        currentStatus = "";
        currentState = ProgressState.IDLE;
        
        Platform.runLater(() -> {
            if (progressBar != null) {
                progressBar.setProgress(0.0);
                progressBar.setVisible(false);
            }
            
            if (progressStatus != null) {
                progressStatus.setText("");
            }
            
            if (processCompletionStatus != null) {
                processCompletionStatus.setText("");
            }
            
            updateStatusIndicator(currentState);
        });
        
        logger.debug("Progress tracking reset");
    }
    
    /**
     * Shows indeterminate progress (spinner-like behavior)
     * 
     * @param message Status message
     */
    public void showIndeterminate(String message) {
        currentStatus = message != null ? message : "Processing...";
        currentState = ProgressState.IN_PROGRESS;
        
        Platform.runLater(() -> {
            if (progressBar != null) {
                progressBar.setProgress(ProgressBar.INDETERMINATE_PROGRESS);
                progressBar.setVisible(true);
            }
            
            if (progressStatus != null) {
                progressStatus.setText(currentStatus);
            }
            
            updateStatusIndicator(currentState);
        });
        
        logger.debug("Indeterminate progress shown: {}", currentStatus);
    }
    
    /**
     * Updates the status indicator color based on current state
     */
    private void updateStatusIndicator(ProgressState state) {
        if (statusIndicator == null) return;
        
        Color color;
        switch (state) {
            case IDLE:
                color = Color.GRAY;
                break;
            case IN_PROGRESS:
                color = Color.ORANGE;
                break;
            case SUCCESS:
                color = Color.GREEN;
                break;
            case ERROR:
                color = Color.RED;
                break;
            case CANCELLED:
                color = Color.GRAY;
                break;
            default:
                color = Color.GRAY;
                break;
        }
        
        statusIndicator.setFill(color);
    }
    
    /**
     * Gets the current progress value
     * 
     * @return Current progress (0.0-1.0)
     */
    public double getCurrentProgress() {
        return currentProgress;
    }
    
    /**
     * Gets the current status message
     * 
     * @return Current status message
     */
    public String getCurrentStatus() {
        return currentStatus;
    }
    
    /**
     * Gets the current progress state
     * 
     * @return Current progress state
     */
    public ProgressState getCurrentState() {
        return currentState;
    }
    
    /**
     * Checks if progress tracking is currently active
     * 
     * @return true if progress is being tracked, false otherwise
     */
    public boolean isActive() {
        return currentState == ProgressState.IN_PROGRESS;
    }
    
    /**
     * Enumeration of possible progress states
     */
    public enum ProgressState {
        IDLE,
        IN_PROGRESS,
        SUCCESS,
        ERROR,
        CANCELLED
    }
}
