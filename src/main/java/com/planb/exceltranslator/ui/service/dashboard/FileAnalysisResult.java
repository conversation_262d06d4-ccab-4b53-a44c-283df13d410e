package com.planb.exceltranslator.ui.service.dashboard;

import com.planb.exceltranslator.domain.model.ExcelSheet;
import com.planb.exceltranslator.domain.model.Language;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * Result of file analysis operation.
 * Uses Lombok for cleaner code.
 */
@Getter
@Builder
public class FileAnalysisResult {
    private final boolean success;
    private final List<ExcelSheet> sheets;
    private final Language detectedLanguage;
    private final String errorMessage;
}
