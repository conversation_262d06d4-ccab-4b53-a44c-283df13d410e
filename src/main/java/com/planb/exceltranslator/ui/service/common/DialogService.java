package com.planb.exceltranslator.ui.service.common;

import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;
import javafx.stage.Modality;
import javafx.stage.Stage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Singleton;
import java.util.Optional;

/**
 * Service for handling various dialog operations.
 * Follows SonarQube standards and uses Java 8 features with Lombok.
 */
@Slf4j
@Singleton
@RequiredArgsConstructor
public class DialogService {
    
    private final LanguageManager languageManager;
    
    /**
     * Shows an error dialog with the given title and message.
     */
    public void showError(String title, String message) {
        showAlert(Alert.AlertType.ERROR, title, message);
    }
    
    /**
     * Shows an information dialog with the given title and message.
     */
    public void showInfo(String title, String message) {
        showAlert(Alert.AlertType.INFORMATION, title, message);
    }
    
    /**
     * Shows a warning dialog with the given title and message.
     */
    public void showWarning(String title, String message) {
        showAlert(Alert.AlertType.WARNING, title, message);
    }
    
    /**
     * Shows a confirmation dialog and returns the user's choice.
     */
    public boolean showConfirmation(String title, String message) {
        Alert alert = createAlert(Alert.AlertType.CONFIRMATION, title, message);
        Optional<ButtonType> result = alert.showAndWait();
        return result.isPresent() && result.get() == ButtonType.OK;
    }
    
    /**
     * Opens the preferences dialog.
     */
    public void openPreferences() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/Preferences.fxml"));
            Parent root = loader.load();
            
            Stage preferencesStage = new Stage();
            preferencesStage.setTitle(languageManager.getString("preferences.title"));
            preferencesStage.setScene(new Scene(root));
            preferencesStage.initModality(Modality.APPLICATION_MODAL);
            preferencesStage.setResizable(false);
            
            // Get the controller and set dependencies
            Object controller = loader.getController();
            if (controller instanceof PreferencesController) {
                ((PreferencesController) controller).setStage(preferencesStage);
            }
            
            preferencesStage.showAndWait();
            
        } catch (Exception e) {
            log.error("Failed to open preferences dialog", e);
            showError("Error", "Failed to open preferences: " + e.getMessage());
        }
    }
    
    /**
     * Shows the about dialog.
     */
    public void showAbout() {
        Alert alert = createAlert(Alert.AlertType.INFORMATION, 
            "About Excel Translator", 
            buildAboutMessage());
        alert.showAndWait();
    }
    
    /**
     * Shows a confirmation dialog for exiting the application.
     */
    public void confirmExit() {
        if (showConfirmation("Exit Application", 
                "Are you sure you want to exit?")) {
            System.exit(0);
        }
    }
    
    private void showAlert(Alert.AlertType type, String title, String message) {
        Alert alert = createAlert(type, title, message);
        alert.showAndWait();
    }
    
    private Alert createAlert(Alert.AlertType type, String title, String message) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        return alert;
    }
    
    private String buildAboutMessage() {
        return "Version: 1.0.0\n\n" +
            "A powerful Excel file translation tool supporting:\n" +
            "• DeepL Translator (Recommended)\n" +
            "• Google Translator\n" +
            "• Multiple languages\n" +
            "• Batch processing\n" +
            "• Automatic backups\n\n" +
            "Developed by PLAN-B\n" +
            "© 2025 All rights reserved.";
    }
    
    // Placeholder interface for PreferencesController
    private interface PreferencesController {
        void setStage(Stage stage);
    }
}
