package com.planb.exceltranslator.ui.factory;

import com.planb.exceltranslator.application.TranslationApplicationService;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import com.planb.exceltranslator.ui.component.UIControlsInitializer;
import com.planb.exceltranslator.ui.component.UIServiceComponents;
import com.planb.exceltranslator.ui.service.*;
import com.planb.exceltranslator.ui.service.UIServices;
import com.planb.exceltranslator.ui.service.impl.*;
import com.planb.exceltranslator.ui.service.impl.FileOperationServiceImpl;
import com.planb.exceltranslator.ui.service.impl.DashboardBusinessServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Factory for creating and wiring UI services with dependency injection
 * Follows the Factory pattern and dependency injection principles
 */
@Slf4j
@RequiredArgsConstructor
public class UIServiceFactory {
    
    private final ConfigurationManager configManager;
    private final LanguageManager languageManager;
    private final TranslationApplicationService translationService;
    
    /**
     * Creates all required UI services with proper dependency injection
     */
    public UIServices createUIServices(UIServiceComponents components) {
        log.debug("Creating UI services with dependency injection");
        
        // Core services
        LoggingService loggingService = createLoggingService(components);
        ProgressService progressService = createProgressService(components);
        SheetsDisplayService sheetsDisplayService = createSheetsDisplayService(components);
        UIStateService uiStateService = createUIStateService(components);
        DialogService dialogService = createDialogService();
        
        // Component services
        UIControlsInitializer controlsInitializer = createControlsInitializer(loggingService);
        FileOperationService fileOperationService = createFileOperationService(
            loggingService, sheetsDisplayService, controlsInitializer);
        DashboardBusinessService businessService = createBusinessService(
            fileOperationService, loggingService, sheetsDisplayService, uiStateService, dialogService);
        
        // Setup service callbacks
        setupServiceCallbacks(progressService);
        
        return UIServices.builder()
            .loggingService(loggingService)
            .progressService(progressService)
            .sheetsDisplayService(sheetsDisplayService)
            .uiStateService(uiStateService)
            .dialogService(dialogService)
            .controlsInitializer(controlsInitializer)
            .fileOperationService(fileOperationService)
            .businessService(businessService)
            .build();
    }
    
    private LoggingService createLoggingService(UIServiceComponents components) {
        return new LoggingServiceImpl(components.getLogArea(), components.getStatusIndicator());
    }
    
    private ProgressService createProgressService(UIServiceComponents components) {
        return new ProgressServiceImpl(
            components.getProgressBar(), 
            components.getProgressStatus(), 
            components.getProcessCompletionStatus()
        );
    }
    
    private SheetsDisplayService createSheetsDisplayService(@SuppressWarnings("unused") UIServiceComponents components) {
        return new SheetsDisplayService();
    }
    
    private UIStateService createUIStateService(UIServiceComponents components) {
        return new UIStateServiceImpl(
            components.getTranslateButton(), 
            components.getExportButton(), 
            languageManager
        );
    }
    
    private DialogService createDialogService() {
        return new DialogService(languageManager);
    }
    
    private UIControlsInitializer createControlsInitializer(LoggingService loggingService) {
        return new UIControlsInitializer(configManager, loggingService::logInfo);
    }
    
    private FileOperationService createFileOperationService(
            LoggingService loggingService, 
            SheetsDisplayService sheetsDisplayService, 
            UIControlsInitializer controlsInitializer) {
        return new FileOperationServiceImpl(
            translationService, 
            configManager, 
            loggingService, 
            sheetsDisplayService, 
            controlsInitializer
        );
    }
    
    private DashboardBusinessService createBusinessService(
            FileOperationService fileOperationService,
            LoggingService loggingService,
            SheetsDisplayService sheetsDisplayService,
            UIStateService uiStateService,
            DialogService dialogService) {
        return new DashboardBusinessServiceImpl(
            fileOperationService, 
            loggingService, 
            sheetsDisplayService, 
            uiStateService, 
            dialogService
        );
    }
    
    private void setupServiceCallbacks(ProgressService progressService) {
        translationService.setProgressCallback(progress -> 
            progressService.updateProgress(progress.getPercent(), progress.getMessage()));
        translationService.setLogCallback(log::info);
    }
}
