package com.planb.exceltranslator.ui.controller;

import com.planb.exceltranslator.application.FileAnalysisService;
import com.planb.exceltranslator.application.LanguageDetectionService;
import com.planb.exceltranslator.application.TranslationApplicationService;
import com.planb.exceltranslator.domain.model.*;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.MouseEvent;
import javafx.stage.FileChooser;
import javafx.stage.Stage;

import java.io.File;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Final compact version of DashboardController under 300 lines
 * Modern Java practices with minimal dependencies
 */
public class DashboardControllerSimple implements Initializable {
    private static final System.Logger logger = System.getLogger(DashboardControllerSimple.class.getName());
    
    // FXML UI Components
    @FXML private Button selectFileButton, translateButton, exportButton, detectLanguageButton;
    @FXML private ComboBox<String> sourceLanguageComboBox, targetLanguageComboBox, translatorTypeComboBox;
    @FXML private TableView<ExcelSheet> sheetsTableView;
    @FXML private TableColumn<ExcelSheet, String> sheetNameColumn, sheetRowsColumn;
    @FXML private CheckBox selectAllSheetsCheckBox;
    @FXML private TextArea logArea;
    @FXML private ProgressBar progressBar;
    @FXML private Label progressStatus, statusIndicator, processCompletionStatus;

    // Services
    private ConfigurationManager configManager;
    private LanguageManager languageManager;
    private TranslationApplicationService translationService;
    
    // State
    private File selectedFile;
    private List<ExcelSheet> availableSheets = new ArrayList<>();

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            logger.log(System.Logger.Level.INFO, "Initializing Dashboard UI components");
            initializeServices();
            initializeControls();
            setupEventHandlers();
            updateUIState();
            logger.log(System.Logger.Level.INFO, "Dashboard initialization completed successfully");
        } catch (Exception e) {
            logger.log(System.Logger.Level.ERROR, "Failed to initialize Dashboard", e);
            showError("Failed to initialize Dashboard: " + e.getMessage());
        }
    }

    private void initializeServices() {
        this.configManager = ConfigurationManager.getInstance();
        this.languageManager = LanguageManager.getInstance();
        this.translationService = new TranslationApplicationService(configManager, languageManager);
    }

    private void initializeControls() {
        // Initialize language combo boxes
        var languages = Arrays.stream(Language.values())
            .map(Enum::name)
            .collect(Collectors.toList());
        sourceLanguageComboBox.setItems(FXCollections.observableArrayList(languages));
        targetLanguageComboBox.setItems(FXCollections.observableArrayList(languages));
        
        // Initialize translator type combo box
        var translatorTypes = Arrays.stream(TranslatorType.values())
            .map(Enum::name)
            .collect(Collectors.toList());
        translatorTypeComboBox.setItems(FXCollections.observableArrayList(translatorTypes));
        
        // Initialize table view
        sheetNameColumn.setCellValueFactory(new PropertyValueFactory<>("sheetName"));
        sheetRowsColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(String.valueOf(cellData.getValue().getRowCount())));
        
        // Set default values
        sourceLanguageComboBox.setValue("ENGLISH");
        targetLanguageComboBox.setValue("JAPANESE");
        translatorTypeComboBox.setValue("GOOGLE_TRANSLATE");
    }

    private void setupEventHandlers() {
        selectFileButton.setOnAction(e -> selectFile());
        translateButton.setOnAction(e -> startTranslation());
        exportButton.setOnAction(e -> exportResults());
        detectLanguageButton.setOnAction(e -> detectLanguage());
        selectAllSheetsCheckBox.setOnAction(e -> toggleAllSheets());
        sheetsTableView.setOnMouseClicked(this::handleSheetSelection);
    }

    @FXML
    private void selectFile() {
        logger.log(System.Logger.Level.INFO, "File selection initiated");
        
        var fileChooser = new FileChooser();
        fileChooser.setTitle("Select Excel File");
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("Excel Files", "*.xlsx", "*.xls"),
            new FileChooser.ExtensionFilter("All Files", "*.*")
        );

        var stage = (Stage) selectFileButton.getScene().getWindow();
        var file = fileChooser.showOpenDialog(stage);
        
        if (file != null) {
            processSelectedFile(file);
        }
    }

    private void processSelectedFile(File file) {
        selectedFile = file;
        statusIndicator.setText("Selected: " + file.getName());
        logger.log(System.Logger.Level.INFO, "File selected: " + file.getAbsolutePath());

        CompletableFuture.supplyAsync(() -> analyzeFile(file))
            .thenAcceptAsync(this::handleFileAnalysisResult, Platform::runLater)
            .exceptionally(this::handleFileAnalysisError);
    }

    private List<ExcelSheet> analyzeFile(File file) {
        try {
            return translationService.analyzeExcelFile(file);
        } catch (Exception e) {
            logger.log(System.Logger.Level.ERROR, "File analysis failed", e);
            throw new RuntimeException("Failed to analyze file: " + e.getMessage(), e);
        }
    }

    private void handleFileAnalysisResult(List<ExcelSheet> sheets) {
        availableSheets = sheets;
        sheetsTableView.setItems(FXCollections.observableArrayList(sheets));
        updateUIState();
        logArea.appendText("File analyzed successfully. Found " + sheets.size() + " sheets.\n");
    }

    private Void handleFileAnalysisError(Throwable throwable) {
        Platform.runLater(() -> {
            logger.log(System.Logger.Level.ERROR, "File analysis error", throwable);
            showError("File analysis failed: " + throwable.getMessage());
        });
        return null;
    }

    @FXML
    private void detectLanguage() {
        if (!validateFileSelection()) return;
        
        logger.log(System.Logger.Level.INFO, "Starting language detection");
        logArea.appendText("Language detection feature will be implemented in a future version.\n");
        showInfo("Language detection feature is not yet implemented.");
    }

    @FXML
    private void startTranslation() {
        if (!validateTranslationInputs()) return;
        
        logger.log(System.Logger.Level.INFO, "Starting translation process");
        
        var task = createTranslationTask();
        bindProgressToTask(task);
        
        task.setOnSucceeded(e -> handleTranslationSuccess());
        task.setOnFailed(e -> handleTranslationFailure(task.getException()));
        
        new Thread(task).start();
        updateUIState();
    }

    private Task<Void> createTranslationTask() {
        return new Task<>() {
            @Override
            protected Void call() throws Exception {
                var selectedSheets = getSelectedSheets();
                var sourceLanguage = Language.valueOf(sourceLanguageComboBox.getValue());
                var targetLanguage = Language.valueOf(targetLanguageComboBox.getValue());
                var translatorType = TranslatorType.valueOf(translatorTypeComboBox.getValue());
                
                updateMessage("Starting translation...");
                updateProgress(0, 100);
                
                // Mock translation process for demonstration
                for (int i = 0; i < selectedSheets.size(); i++) {
                    var sheet = selectedSheets.get(i);
                    updateMessage("Translating sheet: " + sheet.getSheetName());
                    updateProgress(i + 1, selectedSheets.size());
                    Thread.sleep(1000); // Simulate translation time
                }
                
                updateMessage("Translation completed");
                updateProgress(100, 100);
                return null;
            }
        };
    }

    private void bindProgressToTask(Task<?> task) {
        progressBar.progressProperty().bind(task.progressProperty());
        progressStatus.textProperty().bind(task.messageProperty());
    }

    private void handleTranslationSuccess() {
        Platform.runLater(() -> {
            logger.log(System.Logger.Level.INFO, "Translation completed successfully");
            logArea.appendText("Translation completed successfully!\n");
            processCompletionStatus.setText("Translation completed");
            exportButton.setDisable(false);
            updateUIState();
        });
    }

    private void handleTranslationFailure(Throwable exception) {
        Platform.runLater(() -> {
            logger.log(System.Logger.Level.ERROR, "Translation failed", exception);
            showError("Translation failed: " + exception.getMessage());
            processCompletionStatus.setText("Translation failed");
            updateUIState();
        });
    }

    @FXML
    private void exportResults() {
        logger.log(System.Logger.Level.INFO, "Export results requested");
        showInfo("Export functionality will be implemented based on translation results");
    }

    private void toggleAllSheets() {
        boolean selectAll = selectAllSheetsCheckBox.isSelected();
        availableSheets.forEach(sheet -> sheet.setSelected(selectAll));
        sheetsTableView.refresh();
    }

    private void handleSheetSelection(MouseEvent event) {
        if (event.getClickCount() == 2) {
            var selectedSheet = sheetsTableView.getSelectionModel().getSelectedItem();
            if (selectedSheet != null) {
                selectedSheet.setSelected(!selectedSheet.isSelected());
                sheetsTableView.refresh();
            }
        }
    }

    private boolean validateFileSelection() {
        if (selectedFile == null) {
            showError("Please select an Excel file first");
            return false;
        }
        return true;
    }

    private boolean validateTranslationInputs() {
        return validateFileSelection() && 
               validateLanguageSelection() && 
               validateSheetSelection() && 
               validateTranslatorConfiguration();
    }

    private boolean validateLanguageSelection() {
        if (sourceLanguageComboBox.getValue() == null || targetLanguageComboBox.getValue() == null) {
            showError("Please select both source and target languages");
            return false;
        }
        return true;
    }

    private boolean validateSheetSelection() {
        if (getSelectedSheets().isEmpty()) {
            showError("Please select at least one sheet to translate");
            return false;
        }
        return true;
    }

    private boolean validateTranslatorConfiguration() {
        if (translatorTypeComboBox.getValue() == null) {
            showError("Please select a translator type");
            return false;
        }
        return true;
    }

    private List<ExcelSheet> getSelectedSheets() {
        return availableSheets.stream()
            .filter(ExcelSheet::isSelected)
            .collect(Collectors.toList());
    }

    private void updateUIState() {
        boolean fileSelected = selectedFile != null;
        boolean hasSheets = !availableSheets.isEmpty();
        boolean hasSelectedSheets = !getSelectedSheets().isEmpty();
        
        detectLanguageButton.setDisable(!hasSelectedSheets);
        translateButton.setDisable(!hasSelectedSheets || sourceLanguageComboBox.getValue() == null 
                                 || targetLanguageComboBox.getValue() == null);
    }

    private void showError(String message) {
        var alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("Error");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
        logArea.appendText("ERROR: " + message + "\n");
    }

    private void showInfo(String message) {
        var alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Information");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
        logArea.appendText("INFO: " + message + "\n");
    }
}
