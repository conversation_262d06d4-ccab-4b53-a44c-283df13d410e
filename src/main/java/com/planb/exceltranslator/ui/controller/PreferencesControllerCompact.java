package com.planb.exceltranslator.ui.controller;

import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.stage.Stage;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;
import java.util.ResourceBundle;
import java.util.function.Consumer;
import java.util.regex.Pattern;

/**
 * Compact Preferences Controller - under 300 lines with optimized design.
 * Follows SonarQube standards with Java 8 features and Lombok.
 */
@Slf4j
public class PreferencesControllerCompact implements Initializable {
    
    private static final String ENGLISH_DEFAULT = "English (Default)";
    private static final String INTERFACE_LANGUAGE_KEY = "interface.language";
    private static final String DEEPL_API_KEY = "deepl.api.key";
    private static final String GOOGLE_API_KEY = "google.api.key";
    private static final String GREEN_COLOR = "green";
    private static final Pattern DEEPL_PATTERN = Pattern.compile("^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}:fx$");
    private static final Pattern GOOGLE_PATTERN = Pattern.compile("^[A-Za-z0-9_-]{39}$");
    
    // FXML UI Components
    @FXML private ComboBox<String> interfaceLanguageComboBox;
    @FXML private PasswordField deeplApiKeyField;
    @FXML private PasswordField googleApiKeyField;
    @FXML private Label deeplApiStatusIndicator;
    @FXML private Label googleApiStatusIndicator;
    @FXML private Tab interfaceTab;
    @FXML private Tab apiKeysTab;
    @FXML private Label interfaceLanguageLabel;
    @FXML private Label languageNoteLabel;
    @FXML private Label deeplKeyLabel;
    @FXML private Label deeplInfoLabel;
    @FXML private Label googleKeyLabel;
    @FXML private Label googleInfoLabel;
    @FXML private Label securityNoteLabel;
    @FXML private Button saveButton;
    @FXML private Button cancelButton;
    
    // Core Dependencies & State
    private ConfigurationManager configManager;
    private Stage preferencesStage;
    private Consumer<String> logCallback;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("Initializing Preferences Controller...");
        
        try {
            configManager = ConfigurationManager.getInstance();
            setupInterfaceLanguageOptions();
            setupApiKeyValidation();
            
            log.info("Preferences Controller initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize Preferences Controller", e);
            showAlert(Alert.AlertType.ERROR, "Initialization Error", 
                "Failed to initialize preferences: " + e.getMessage());
        }
    }
    
    private void setupInterfaceLanguageOptions() {
        var languages = FXCollections.observableArrayList(
            ENGLISH_DEFAULT, "日本語 (Japanese)", "Tiếng Việt (Vietnamese)");
        
        interfaceLanguageComboBox.setItems(languages);
        interfaceLanguageComboBox.setValue(ENGLISH_DEFAULT);
    }
    
    private void setupApiKeyValidation() {
        deeplApiKeyField.textProperty().addListener((obs, oldVal, newVal) -> 
            validateApiKey(newVal, deeplApiStatusIndicator, DEEPL_PATTERN));
        
        googleApiKeyField.textProperty().addListener((obs, oldVal, newVal) -> 
            validateApiKey(newVal, googleApiStatusIndicator, GOOGLE_PATTERN));
    }
    
    @FXML
    private void handleSave() {
        try {
            log.info("Saving preferences...");
            logMessage("Saving preferences changes...");
            
            boolean languageChanged = saveLanguageSettings();
            boolean apiKeysUpdated = saveApiKeySettings();
            
            configManager.saveUserPreferences();
            
            if (apiKeysUpdated) {
                log.info("Translation services need refresh due to API key changes");
                logMessage("Translation services will be refreshed with new API keys");
            }
            
            showSuccessMessage();
            logFinalSaveMessage(languageChanged, apiKeysUpdated);
            closeWindow();
            
        } catch (Exception e) {
            log.error("Failed to save preferences", e);
            showAlert(Alert.AlertType.ERROR, "Save Error", "Failed to save preferences: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleCancel() {
        log.info("Preferences cancelled by user");
        closeWindow();
    }
    
    @FXML
    private void handleDeeplKeyFieldFocus() {
        validateApiKey(deeplApiKeyField.getText(), deeplApiStatusIndicator, DEEPL_PATTERN);
    }

    @FXML
    private void handleGoogleKeyFieldFocus() {
        validateApiKey(googleApiKeyField.getText(), googleApiStatusIndicator, GOOGLE_PATTERN);
    }
    
    // Public setters for external dependencies
    public void setPreferencesStage(Stage stage) {
        this.preferencesStage = stage;
        loadCurrentSettings();
    }
    
    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
    
    // Helper Methods
    private boolean saveLanguageSettings() {
        String selectedLanguage = interfaceLanguageComboBox.getValue();
        String languageCode = getLanguageCode(selectedLanguage);
        
        String currentLanguage = configManager.getUserPreference(INTERFACE_LANGUAGE_KEY);
        boolean languageChanged = !languageCode.equals(currentLanguage);
        
        if (languageChanged) {
            var languageManager = LanguageManager.getInstance();
            languageManager.setLanguage(languageCode);
            configManager.setUserPreference(INTERFACE_LANGUAGE_KEY, languageCode);
            
            String languageName = extractLanguageName(selectedLanguage);
            logMessage("Interface language changed to: " + languageName);
            log.info("Interface language changed from '{}' to '{}'", currentLanguage, languageCode);
        }
        
        return languageChanged;
    }
    
    private String getLanguageCode(String selectedLanguage) {
        if (selectedLanguage.contains("Japanese")) return "ja";
        if (selectedLanguage.contains("Vietnamese")) return "vi";
        return "en";
    }
    
    private String extractLanguageName(String selectedLanguage) {
        return selectedLanguage.contains("(") ? 
            selectedLanguage.substring(0, selectedLanguage.indexOf('(')).trim() : selectedLanguage;
    }
    
    private boolean saveApiKeySettings() {
        boolean updated = false;
        
        String deeplKey = deeplApiKeyField.getText();
        if (!deeplKey.isEmpty()) {
            configManager.setUserPreference(DEEPL_API_KEY, deeplKey);
            log.info("DeepL API key updated");
            logMessage("DeepL API key updated successfully");
            updated = true;
        }

        String googleKey = googleApiKeyField.getText();
        if (!googleKey.isEmpty()) {
            configManager.setUserPreference(GOOGLE_API_KEY, googleKey);
            log.info("Google API key updated");
            logMessage("Google Translate API key updated successfully");
            updated = true;
        }
        
        return updated;
    }
    
    private void loadCurrentSettings() {
        // Load language setting
        String currentLanguage = configManager.getUserPreference(INTERFACE_LANGUAGE_KEY);
        interfaceLanguageComboBox.setValue(mapLanguageCodeToDisplay(currentLanguage));
        
        // Load API keys (masked)
        if (isKeyConfigured(DEEPL_API_KEY)) {
            deeplApiKeyField.setPromptText("API key configured");
            updateStatusIndicator(deeplApiStatusIndicator, "✓ Configured", GREEN_COLOR);
        }
        
        if (isKeyConfigured(GOOGLE_API_KEY)) {
            googleApiKeyField.setPromptText("API key configured");
            updateStatusIndicator(googleApiStatusIndicator, "✓ Configured", GREEN_COLOR);
        }
        
        log.debug("Current settings loaded into UI");
    }
    
    private boolean isKeyConfigured(String keyName) {
        String key = configManager.getUserPreference(keyName);
        return key != null && !key.isEmpty();
    }
    
    private String mapLanguageCodeToDisplay(String languageCode) {
        if (languageCode == null) return ENGLISH_DEFAULT;
        
        switch (languageCode) {
            case "ja": return "日本語 (Japanese)";
            case "vi": return "Tiếng Việt (Vietnamese)";
            default: return ENGLISH_DEFAULT;
        }
    }
    
    private void validateApiKey(String apiKey, Label statusIndicator, Pattern pattern) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            updateStatusIndicator(statusIndicator, "Not configured", "gray");
            return;
        }
        
        if (pattern.matcher(apiKey.trim()).matches()) {
            updateStatusIndicator(statusIndicator, "✓ Valid format", GREEN_COLOR);
        } else {
            updateStatusIndicator(statusIndicator, "✗ Invalid format", "red");
        }
    }
    
    private void updateStatusIndicator(Label indicator, String text, String color) {
        indicator.setText(text);
        indicator.setStyle("-fx-text-fill: " + color + ";");
    }
    
    private void showSuccessMessage() {
        var languageManager = LanguageManager.getInstance();
        showAlert(Alert.AlertType.INFORMATION, 
            languageManager.getString("preferences.saved.title"),
            languageManager.getString("preferences.saved.message"));
    }
    
    private void logFinalSaveMessage(boolean languageChanged, boolean apiKeysUpdated) {
        String message;
        if (languageChanged && apiKeysUpdated) {
            message = "Preferences saved: Language and API keys updated successfully";
        } else if (languageChanged) {
            message = "Preferences saved: Interface language updated";
        } else if (apiKeysUpdated) {
            message = "Preferences saved: API keys updated successfully";
        } else {
            message = "Preferences saved: No changes were made";
        }
        
        log.info(message);
        logMessage(message);
    }
    
    private void logMessage(String message) {
        log.info(message);
        if (logCallback != null) {
            logCallback.accept(message);
        }
    }
    
    private void closeWindow() {
        if (preferencesStage != null) {
            preferencesStage.close();
        }
    }
    
    private void showAlert(Alert.AlertType type, String title, String message) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
