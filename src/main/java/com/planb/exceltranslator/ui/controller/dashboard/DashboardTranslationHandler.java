package com.planb.exceltranslator.ui.controller.dashboard;

import com.planb.exceltranslator.application.TranslationApplicationService;
import com.planb.exceltranslator.domain.model.Language;
import com.planb.exceltranslator.domain.model.TranslatorType;
import javafx.application.Platform;
import javafx.concurrent.Task;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * Service responsible for handling translation operations in the Dashboard.
 * Follows SonarQube standards and uses Java 8 features with Lombok.
 */
@Slf4j
@RequiredArgsConstructor
public class DashboardTranslationHandler {
    
    private final TranslationApplicationService translationService;
    
    private Consumer<String> logCallback;
    private Consumer<Double> progressCallback;
    private Consumer<Boolean> translationStateCallback;
    private Runnable onTranslationComplete;
    
    private Task<?> currentTranslationTask;
    private boolean isTranslating = false;
    
    /**
     * Sets the callback for logging events
     * @param callback The callback to execute for logging
     */
    public void setLogCallback(Consumer<String> callback) {
        this.logCallback = callback;
    }
    
    /**
     * Sets the callback for progress updates
     * @param callback The callback to execute for progress updates
     */
    public void setProgressCallback(Consumer<Double> callback) {
        this.progressCallback = callback;
    }
    
    /**
     * Sets the callback for translation state changes
     * @param callback The callback to execute when translation state changes
     */
    public void setTranslationStateCallback(Consumer<Boolean> callback) {
        this.translationStateCallback = callback;
    }
    
    /**
     * Sets the callback for translation completion
     * @param callback The callback to execute when translation completes
     */
    public void setOnTranslationComplete(Runnable callback) {
        this.onTranslationComplete = callback;
    }
    
    /**
     * Starts translation process
     * @param sourceLanguage Source language for translation
     * @param targetLanguage Target language for translation
     * @param translatorType Translation service to use
     * @param batchSize Batch size for translation
     */
    public void startTranslation(Language sourceLanguage, Language targetLanguage, 
                               TranslatorType translatorType, int batchSize) {
        if (isTranslating) {
            log.warn("Translation already in progress");
            return;
        }
        
        if (!validateTranslationParameters(sourceLanguage, targetLanguage, translatorType)) {
            return;
        }
        
        setTranslating(true);
        logMessage("Starting translation: " + sourceLanguage.getEnglishName() + " → " + targetLanguage.getEnglishName());
        
        currentTranslationTask = createTranslationTask(sourceLanguage, targetLanguage, translatorType, batchSize);
        new Thread(currentTranslationTask).start();
    }
    
    /**
     * Cancels the current translation process
     */
    public void cancelTranslation() {
        if (!isTranslating) {
            log.debug("No translation in progress to cancel");
            return;
        }
        
        logMessage("Cancelling translation...");
        
        Optional.ofNullable(currentTranslationTask)
                .ifPresent(Task::cancel);
        
        setTranslating(false);
        logMessage("Translation cancelled by user");
    }
    
    /**
     * Checks if translation is currently in progress
     * @return true if translating, false otherwise
     */
    public boolean isTranslating() {
        return isTranslating;
    }
    
    /**
     * Validates translation parameters
     * @param sourceLanguage Source language
     * @param targetLanguage Target language
     * @param translatorType Translator type
     * @return true if valid, false otherwise
     */
    private boolean validateTranslationParameters(Language sourceLanguage, Language targetLanguage, 
                                                 TranslatorType translatorType) {
        if (sourceLanguage == null) {
            logMessage("Error: Source language not selected");
            return false;
        }
        
        if (targetLanguage == null) {
            logMessage("Error: Target language not selected");
            return false;
        }
        
        if (translatorType == null) {
            logMessage("Error: Translator not selected");
            return false;
        }
        
        if (sourceLanguage.equals(targetLanguage)) {
            logMessage("Warning: Source and target languages are the same");
        }
        
        return true;
    }
    
    /**
     * Creates a translation task
     * @param sourceLanguage Source language
     * @param targetLanguage Target language
     * @param translatorType Translator type
     * @param batchSize Batch size
     * @return Translation task
     */
    private Task<Void> createTranslationTask(Language sourceLanguage, Language targetLanguage, 
                                           TranslatorType translatorType, int batchSize) {
        return new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                try {
                    // Note: This is a simplified implementation
                    // The actual translation would require a TranslationRequest object
                    // with file and sheets information
                    logMessage("Translation task started with parameters:");
                    logMessage("Source: " + sourceLanguage.getEnglishName());
                    logMessage("Target: " + targetLanguage.getEnglishName());
                    logMessage("Translator: " + translatorType.name());
                    logMessage("Batch Size: " + batchSize);
                    
                    // Simulate progress updates
                    Platform.runLater(() -> DashboardTranslationHandler.this.updateProgress(0.0));
                    Thread.sleep(1000);
                    Platform.runLater(() -> DashboardTranslationHandler.this.updateProgress(0.3));
                    Thread.sleep(1000);
                    Platform.runLater(() -> DashboardTranslationHandler.this.updateProgress(0.6));
                    Thread.sleep(1000);
                    Platform.runLater(() -> DashboardTranslationHandler.this.updateProgress(0.9));
                    Thread.sleep(500);
                    Platform.runLater(() -> DashboardTranslationHandler.this.updateProgress(1.0));
                    
                    Platform.runLater(DashboardTranslationHandler.this::handleTranslationSuccess);
                    
                } catch (InterruptedException e) {
                    log.info("Translation task interrupted");
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    Platform.runLater(() -> DashboardTranslationHandler.this.handleTranslationError(e));
                }
                
                return null;
            }
        };
    }
    
    /**
     * Handles translation success
     */
    private void handleTranslationSuccess() {
        logMessage("Translation completed successfully");
        updateProgress(1.0);
        
        Optional.ofNullable(onTranslationComplete)
                .ifPresent(Runnable::run);
    }
    
    /**
     * Handles translation errors
     * @param throwable The error that occurred
     */
    private void handleTranslationError(Throwable throwable) {
        log.error("Translation failed", throwable);
        logMessage("Translation failed: " + throwable.getMessage());
        updateProgress(0.0);
    }
    
    /**
     * Updates translation state
     * @param translating New translation state
     */
    private void setTranslating(boolean translating) {
        this.isTranslating = translating;
        
        Optional.ofNullable(translationStateCallback)
                .ifPresent(callback -> callback.accept(translating));
    }
    
    /**
     * Updates progress
     * @param progress Progress value (0.0 to 1.0)
     */
    private void updateProgress(double progress) {
        Optional.ofNullable(progressCallback)
                .ifPresent(callback -> callback.accept(progress));
    }
    
    /**
     * Logs a message using the configured callback
     * @param message The message to log
     */
    private void logMessage(String message) {
        Optional.ofNullable(logCallback)
                .ifPresent(callback -> callback.accept(message));
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        cancelTranslation();
        log.debug("DashboardTranslationHandler cleanup completed");
    }
}
