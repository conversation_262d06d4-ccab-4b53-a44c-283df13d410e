package com.planb.exceltranslator.ui.controller;

import com.planb.exceltranslator.domain.model.*;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import com.planb.exceltranslator.ui.controller.dashboard.DashboardTranslationHandler;
import com.planb.exceltranslator.ui.service.dashboard.DashboardProgressHandler;
import com.planb.exceltranslator.ui.service.dashboard.DashboardLanguageHandler;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.Pane;
import javafx.scene.shape.Circle;
import javafx.stage.FileChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;

/**
 * Optimized Dashboard Controller - under 300 lines.
 * Uses delegation pattern with specialized service classes.
 * Follows SonarQube standards with Java 8 features and Lombok.
 */
@Slf4j
public class DashboardControllerOptimized implements Initializable {
    
    // FXML UI Components - Main Controls
    @FXML private Pane fileDragAndDrop;
    @FXML private Label selectedFile;
    @FXML private Button selectFileButton;
    @FXML private Label fileSize;
    @FXML private ComboBox<Language> sourceLanguage;
    @FXML private ComboBox<Language> targetLanguage;
    @FXML private ComboBox<TranslatorType> translator;
    @FXML private Spinner<Integer> batchSize;
    @FXML private ProgressBar progressBar;
    @FXML private Label progressStatus;
    @FXML private Label processCompletionStatus;
    @FXML private Circle statusIndicator;
    @FXML private AnchorPane sheetsDetectedList;
    @FXML private ScrollPane sheetsScrollPane;
    @FXML private TextArea logArea;
    @FXML private Button translateButton;
    @FXML private Button cancelButton;
    @FXML private Button exportButton;
    @FXML private MenuItem selectFileMenuItem;
    @FXML private MenuItem exportFileMenuItem;
    @FXML private MenuItem preferencesMenuItem;
    @FXML private MenuItem aboutMenuItem;
    @FXML private MenuItem exitMenuItem;
    
    // Core Dependencies & Services
    private ConfigurationManager configManager;
    private LanguageManager languageManager;
    private DashboardTranslationHandler translationHandler;
    private DashboardProgressHandler progressHandler;
    private DashboardLanguageHandler languageHandler;
    
    // Application State
    private File selectedExcelFile;
    private List<ExcelSheet> detectedSheets;
    private TranslationResult lastTranslationResult;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("Initializing Dashboard Controller...");
        
        try {
            initializeServices();
            setupEventHandlers();
            initializeUIComponents();
            updateUIState();
            
            log.info("Dashboard Controller initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize Dashboard Controller", e);
            showError("Initialization Error", "Failed to initialize: " + e.getMessage());
        }
    }
    
    private void initializeServices() {
        configManager = ConfigurationManager.getInstance();
        languageManager = LanguageManager.getInstance();
        translationHandler = new DashboardTranslationHandler(null);
        progressHandler = new DashboardProgressHandler();
        languageHandler = new DashboardLanguageHandler(languageManager);
        
        progressHandler.initialize(progressBar, progressStatus, processCompletionStatus, statusIndicator);
        languageHandler.initialize(this::onLanguageChanged);
        setupTranslationCallbacks();
    }
    
    private void setupEventHandlers() {
        // File operations
        selectFileButton.setOnAction(e -> selectFile());
        selectFileMenuItem.setOnAction(e -> selectFile());
        
        // Translation operations
        translateButton.setOnAction(e -> startTranslation());
        cancelButton.setOnAction(e -> cancelTranslation());
        exportButton.setOnAction(e -> exportTranslatedFile());
        exportFileMenuItem.setOnAction(e -> exportTranslatedFile());
        
        // Menu operations
        preferencesMenuItem.setOnAction(e -> openPreferences());
        aboutMenuItem.setOnAction(e -> showAbout());
        exitMenuItem.setOnAction(e -> confirmExit());
        
        // Language change handlers
        languageHandler.setupLanguageBindings(sourceLanguage, targetLanguage);
        translator.valueProperty().addListener((obs, oldVal, newVal) -> 
            log.debug("Translator changed to: {}", newVal != null ? newVal.name() : "null"));
        batchSize.valueProperty().addListener((obs, oldVal, newVal) -> 
            log.debug("Batch size changed to: {}", newVal));
    }
    
    private void initializeUIComponents() {
        // Initialize combo boxes
        sourceLanguage.getItems().addAll(Language.getSourceLanguages());
        sourceLanguage.setValue(Language.AUTO_DETECT);
        targetLanguage.getItems().addAll(Language.getTargetLanguages());
        targetLanguage.setValue(Language.ENGLISH);
        translator.getItems().addAll(TranslatorType.values());
        translator.setValue(TranslatorType.getDefault());
        
        // Initialize spinner
        batchSize.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(
            1, 1000, configManager.getDefaultBatchSize()));
        batchSize.setEditable(true);
    }
    
    private void updateUIState() {
        boolean hasFile = selectedExcelFile != null;
        boolean hasSheets = detectedSheets != null && !detectedSheets.isEmpty();
        boolean hasResult = lastTranslationResult != null;
        boolean isTranslating = translationHandler.isTranslating();
        
        translateButton.setDisable(!hasSheets || isTranslating);
        cancelButton.setDisable(!isTranslating);
        exportButton.setDisable(!hasResult);
        exportFileMenuItem.setDisable(!hasResult);
        
        updateFileDisplay(hasFile);
        progressHandler.updateUIState(isTranslating);
    }
    
    // Event Handlers - File Operations
    private void selectFile() {
        FileChooser fileChooser = createFileChooser("Select Excel File");
        Stage stage = (Stage) selectFileButton.getScene().getWindow();
        File file = fileChooser.showOpenDialog(stage);
        
        if (file != null) {
            handleFileSelection(file);
        }
    }
    
    private void handleFileSelection(File file) {
        selectedExcelFile = file;
        updateFileDisplay(true);
        log.info("File selected: {}", file.getName());
        updateUIState();
    }
    
    // Event Handlers - Translation Operations
    private void startTranslation() {
        if (!validateTranslationInputs()) return;
        
        translationHandler.startTranslation(
            sourceLanguage.getValue(),
            targetLanguage.getValue(),
            translator.getValue(),
            batchSize.getValue()
        );
        updateUIState();
    }
    
    private void cancelTranslation() {
        translationHandler.cancelTranslation();
        updateUIState();
    }
    
    private void exportTranslatedFile() {
        if (lastTranslationResult == null) {
            showError("Export Error", "No translation result available for export.");
            return;
        }
        
        FileChooser fileChooser = createFileChooser("Save Translated File");
        if (selectedExcelFile != null) {
            String originalName = selectedExcelFile.getName();
            String baseName = originalName.substring(0, originalName.lastIndexOf('.'));
            String extension = originalName.substring(originalName.lastIndexOf('.'));
            fileChooser.setInitialFileName(baseName + "_translated" + extension);
        }
        
        Stage stage = (Stage) exportButton.getScene().getWindow();
        File outputFile = fileChooser.showSaveDialog(stage);
        
        if (outputFile != null) {
            showInfo("Export Success", "File will be exported to: " + outputFile.getAbsolutePath());
        }
    }
    
    // Event Handlers - Menu Operations
    private void openPreferences() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/Preferences.fxml"));
            Parent root = loader.load();
            
            Stage preferencesStage = new Stage();
            preferencesStage.setTitle("Preferences");
            preferencesStage.setScene(new Scene(root));
            preferencesStage.initModality(Modality.APPLICATION_MODAL);
            preferencesStage.setResizable(false);
            preferencesStage.showAndWait();
        } catch (Exception e) {
            log.error("Failed to open preferences dialog", e);
            showError("Error", "Failed to open preferences: " + e.getMessage());
        }
    }
    
    private void showAbout() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("About Excel Translator");
        alert.setHeaderText("Excel Translator - PLAN-B");
        alert.setContentText("""
            Version: 1.0.0
            
            A powerful Excel file translation tool supporting:
            • DeepL Translator (Recommended)
            • Google Translator
            • Multiple languages
            • Batch processing
            • Automatic backups
            
            Developed by PLAN-B
            © 2025 All rights reserved.""");
        alert.showAndWait();
    }
    
    private void confirmExit() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Exit Application");
        alert.setContentText("Are you sure you want to exit?");
        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                System.exit(0);
            }
        });
    }
    
    // Helper Methods
    private FileChooser createFileChooser(String title) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle(title);
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("Excel Files", "*.xlsx", "*.xls"),
            new FileChooser.ExtensionFilter("All Files", "*.*")
        );
        return fileChooser;
    }
    
    private boolean validateTranslationInputs() {
        if (selectedExcelFile == null) {
            showError("Translation Error", "Please select a file first.");
            return false;
        }
        if (targetLanguage.getValue() == null || translator.getValue() == null) {
            showError("Translation Error", "Please select target language and translator.");
            return false;
        }
        return true;
    }
    
    private void updateFileDisplay(boolean hasFile) {
        if (hasFile && selectedExcelFile != null) {
            selectedFile.setText(selectedExcelFile.getName());
            fileSize.setText(formatFileSize(selectedExcelFile.length()));
        } else {
            selectedFile.setText("No file selected");
            fileSize.setText("");
        }
    }
    
    private String formatFileSize(long sizeInBytes) {
        if (sizeInBytes < 1024) return sizeInBytes + " B";
        int exp = (int) (Math.log(sizeInBytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", sizeInBytes / Math.pow(1024, exp), pre);
    }
    
    private void setupTranslationCallbacks() {
        translationHandler.setLogCallback(this::logMessage);
        translationHandler.setProgressCallback(progress -> 
            progressHandler.updateProgress(progress, "Processing..."));
        translationHandler.setTranslationStateCallback(this::onTranslationStateChanged);
        translationHandler.setOnTranslationComplete(this::onTranslationComplete);
    }
    
    // Callback Methods
    private void onLanguageChanged(Language source, Language target) {
        languageHandler.handleLanguageChange(source, target, targetLanguage);
    }
    
    private void onTranslationStateChanged(Boolean isTranslating) {
        updateUIState();
    }
    
    private void onTranslationComplete() {
        showInfo("Translation Complete", "Translation finished successfully!");
    }
    
    private void logMessage(String message) {
        if (logArea != null) {
            logArea.appendText(message + "\n");
        }
        log.info(message);
    }
    
    private void showError(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showInfo(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
