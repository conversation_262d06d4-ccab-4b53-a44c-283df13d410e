package com.planb.exceltranslator.ui.controller;

import com.planb.exceltranslator.application.TranslationApplicationService;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import com.planb.exceltranslator.ui.preferences.PreferencesUIManager;
import com.planb.exceltranslator.ui.preferences.PreferencesValidator;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.util.ResourceBundle;
import java.util.function.Consumer;

/**
 * Refactored Controller for the Preferences window
 * Delegates complex functionality to specialized components
 */
public class PreferencesController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(PreferencesController.class);
    
    @FXML private ComboBox<String> interfaceLanguageComboBox;
    @FXML private PasswordField deeplApiKeyField;
    @FXML private PasswordField googleApiKeyField;
    @FXML private Label deeplApiStatusIndicator;
    @FXML private Label googleApiStatusIndicator;
    
    @FXML private Tab interfaceTab;
    @FXML private Tab apiKeysTab;
    @FXML private Label interfaceLanguageLabel;
    @FXML private Label languageNoteLabel;
    @FXML private Label deeplKeyLabel;
    @FXML private Label deeplInfoLabel;
    @FXML private Label googleKeyLabel;
    @FXML private Label googleInfoLabel;
    @FXML private Label securityNoteLabel;
    @FXML private Button saveButton;
    @FXML private Button cancelButton;
    
    private ConfigurationManager configManager;
    private LanguageManager languageManager;
    private TranslationApplicationService translationService;
    private Stage preferencesStage;
    private Consumer<String> logCallback;
    
    // Specialized components
    private PreferencesUIManager uiManager;
    private PreferencesValidator validator;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.info("Initializing Preferences Controller...");
        
        configManager = ConfigurationManager.getInstance();
        languageManager = LanguageManager.getInstance();
        
        uiManager = new PreferencesUIManager(configManager, languageManager);
        validator = new PreferencesValidator();
        
        setupInterfaceLanguageOptions();
        setupApiKeyValidation();

        logger.info("Preferences Controller initialized successfully");
    }
    
    private void setupInterfaceLanguageOptions() {
        ObservableList<String> languages = FXCollections.observableArrayList(
            "English (Default)",
            "日本語 (Japanese)", 
            "Tiếng Việt (Vietnamese)"
        );
        
        interfaceLanguageComboBox.setItems(languages);
        interfaceLanguageComboBox.setValue("English (Default)");
    }
    
    private void setupApiKeyValidation() {
        validator.setupApiKeyValidation(deeplApiKeyField, googleApiKeyField,
                                      deeplApiStatusIndicator, googleApiStatusIndicator, uiManager);
    }
    
    @FXML
    private void handleSave() {
        try {
            logger.info("Saving preferences...");
            logMessage("Saving preferences changes...");
            
            boolean languageChanged = saveLanguageSettings();
            boolean apiKeysUpdated = saveApiKeySettings();
            
            configManager.saveUserPreferences();
            
            if (apiKeysUpdated && translationService != null) {
                translationService.refreshTranslationServices();
                logger.info("Translation services refreshed due to API key changes");
                logMessage("Translation services refreshed with new API keys");
            }
            
            showSuccessMessage();
            logFinalSaveMessage(languageChanged, apiKeysUpdated);
            closePreferencesWindow();
            
        } catch (Exception e) {
            logger.error("Failed to save preferences", e);
            showErrorMessage(e);
        }
    }
    
    private boolean saveLanguageSettings() {
        String selectedLanguage = interfaceLanguageComboBox.getValue();
        String languageCode = getLanguageCode(selectedLanguage);
        
        String currentLanguage = configManager.getUserPreference("interface.language");
        boolean languageChanged = !languageCode.equals(currentLanguage);
        
        if (languageChanged) {
            languageManager.setLanguage(languageCode);
            configManager.setUserPreference("interface.language", languageCode);
            
            String languageName = selectedLanguage.contains("(") ? 
                selectedLanguage.substring(0, selectedLanguage.indexOf("(")).trim() : selectedLanguage;
            logMessage("Interface language changed to: " + languageName);
            logger.info("Interface language changed from '{}' to '{}'", currentLanguage, languageCode);
        }
        
        return languageChanged;
    }
    
    private String getLanguageCode(String selectedLanguage) {
        switch (selectedLanguage) {
            case "日本語 (Japanese)":
                return "ja";
            case "Tiếng Việt (Vietnamese)":
                return "vi";
            default:
                return "en";
        }
    }
    
    private boolean saveApiKeySettings() {
        boolean apiKeysUpdated = false;
        
        String deeplKey = deeplApiKeyField.getText();
        if (!deeplKey.isEmpty()) {
            configManager.setUserPreference("deepl.api.key", deeplKey);
            logger.info("DeepL API key updated");
            logMessage("DeepL API key updated successfully");
            apiKeysUpdated = true;
        }

        String googleKey = googleApiKeyField.getText();
        if (!googleKey.isEmpty()) {
            configManager.setUserPreference("google.api.key", googleKey);
            logger.info("Google API key updated");
            logMessage("Google Translate API key updated successfully");
            apiKeysUpdated = true;
        }
        
        return apiKeysUpdated;
    }
    
    private void showSuccessMessage() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(languageManager.getString("preferences.saved.title"));
        alert.setHeaderText(null);
        alert.setContentText(languageManager.getString("preferences.saved.message"));
        alert.showAndWait();
    }
    
    private void showErrorMessage(Exception e) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("Error");
        alert.setHeaderText("Failed to save preferences");
        alert.setContentText("An error occurred while saving your preferences: " + e.getMessage());
        alert.showAndWait();
    }
    
    private void logFinalSaveMessage(boolean languageChanged, boolean apiKeysUpdated) {
        if (languageChanged && apiKeysUpdated) {
            logger.info("Preferences saved successfully - Language and API keys updated");
            logMessage("Preferences saved: Language and API keys updated successfully");
        } else if (languageChanged) {
            logger.info("Preferences saved successfully - Language changed");
            logMessage("Preferences saved: Interface language updated");
        } else if (apiKeysUpdated) {
            logger.info("Preferences saved successfully - API keys updated");
            logMessage("Preferences saved: API keys updated successfully");
        } else {
            logger.info("Preferences saved successfully - No changes detected");
            logMessage("Preferences saved: No changes were made");
        }
    }
    
    @FXML
    private void handleCancel() {
        logger.info("Preferences cancelled by user");
        closePreferencesWindow();
    }
    
    public void setPreferencesStage(Stage stage) {
        this.preferencesStage = stage;
        loadCurrentSettings();
        bindUIElementsToLanguage();
    }
    
    public void setTranslationService(TranslationApplicationService translationService) {
        this.translationService = translationService;
    }
    
    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
    
    private void loadCurrentSettings() {
        uiManager.loadCurrentSettings(interfaceLanguageComboBox, deeplApiKeyField, googleApiKeyField,
                                    deeplApiStatusIndicator, googleApiStatusIndicator);
    }
    
    private void bindUIElementsToLanguage() {
        uiManager.bindUIElementsToLanguage(interfaceTab, apiKeysTab, interfaceLanguageLabel, languageNoteLabel,
                                         deeplKeyLabel, deeplInfoLabel, googleKeyLabel, googleInfoLabel,
                                         securityNoteLabel, saveButton, cancelButton, interfaceLanguageComboBox,
                                         deeplApiKeyField, googleApiKeyField, preferencesStage);
    }
    
    private void logMessage(String message) {
        logger.info(message);
        if (logCallback != null) {
            logCallback.accept(message);
        }
    }
    
    private void closePreferencesWindow() {
        if (preferencesStage != null) {
            preferencesStage.close();
        }
    }
    
    @FXML
    private void handleDeeplKeyFieldFocus() {
        // Field focus handling is now managed by the validator
    }

    @FXML
    private void handleGoogleKeyFieldFocus() {
        // Field focus handling is now managed by the validator
    }
}
