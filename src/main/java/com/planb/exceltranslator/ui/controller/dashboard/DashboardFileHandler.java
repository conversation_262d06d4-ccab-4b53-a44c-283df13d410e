package com.planb.exceltranslator.ui.controller.dashboard;

import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.application.Platform;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * Service responsible for handling file operations in the Dashboard.
 * Follows SonarQube standards and uses Java 8 features.
 */
@Slf4j
@RequiredArgsConstructor
public class DashboardFileHandler {
    
    private final LanguageManager languageManager;
    
    private Consumer<File> fileSelectionCallback;
    private Consumer<String> logCallback;
    
    /**
     * Sets the callback for file selection events
     * @param callback The callback to execute when a file is selected
     */
    public void setFileSelectionCallback(Consumer<File> callback) {
        this.fileSelectionCallback = callback;
    }
    
    /**
     * Sets the callback for logging events
     * @param callback The callback to execute for logging
     */
    public void setLogCallback(Consumer<String> callback) {
        this.logCallback = callback;
    }
    
    /**
     * Handles file selection using a file chooser dialog
     * @param ownerStage The parent stage for the dialog
     */
    public void handleFileSelection(Stage ownerStage) {
        log.debug("Opening file selection dialog");
        
        FileChooser fileChooser = createFileChooser();
        
        Optional.ofNullable(fileChooser.showOpenDialog(ownerStage))
                .ifPresentOrElse(
                    this::processSelectedFile,
                    () -> log.debug("File selection cancelled by user")
                );
    }
    
    /**
     * Processes a file dropped via drag and drop
     * @param file The file to process
     */
    public void handleFileDropped(File file) {
        log.debug("Processing dropped file: {}", file.getName());
        processSelectedFile(file);
    }
    
    /**
     * Creates and configures a FileChooser
     * @return Configured FileChooser instance
     */
    private FileChooser createFileChooser() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle(languageManager.getString("file.chooser.title"));
        
        // Configure file extensions
        FileChooser.ExtensionFilter excelFilter = new FileChooser.ExtensionFilter(
            languageManager.getString("file.chooser.excel.filter"), "*.xlsx", "*.xls");
        fileChooser.getExtensionFilters().add(excelFilter);
        
        return fileChooser;
    }
    
    /**
     * Processes the selected file and triggers analysis
     * @param file The file to process
     */
    private void processSelectedFile(File file) {
        if (!isValidExcelFile(file)) {
            logMessage("Invalid file selected: " + file.getName());
            return;
        }
        
        logMessage("File selected: " + file.getName());
        
        // Trigger file selection callback
        Optional.ofNullable(fileSelectionCallback)
                .ifPresent(callback -> callback.accept(file));
        
        // Perform language detection asynchronously
        detectLanguageAsync(file);
    }
    
    /**
     * Validates if the file is a valid Excel file
     * @param file The file to validate
     * @return true if valid Excel file, false otherwise
     */
    private boolean isValidExcelFile(File file) {
        if (!file.exists() || !file.isFile()) {
            return false;
        }
        
        String fileName = file.getName().toLowerCase();
        return fileName.endsWith(".xlsx") || fileName.endsWith(".xls");
    }
    
    /**
     * Performs language detection asynchronously
     * Note: Language detection requires Excel sheets analysis, not just file
     * This is a placeholder for future file-based detection
     * @param file The file to analyze
     */
    private void detectLanguageAsync(File file) {
        Platform.runLater(() -> {
            try {
                // Note: Actual language detection would require loading the file
                // and extracting sheets first. For now, just log the file selection.
                logMessage("File ready for analysis: " + file.getName());
                logMessage("Language detection will be performed after file analysis");
            } catch (Exception e) {
                log.error("Failed to prepare file for language detection: {}", file.getName(), e);
                logMessage("Error preparing file: " + e.getMessage());
            }
        });
    }
    
    /**
     * Logs a message using the configured callback
     * @param message The message to log
     */
    private void logMessage(String message) {
        Optional.ofNullable(logCallback)
                .ifPresent(callback -> callback.accept(message));
    }
}
