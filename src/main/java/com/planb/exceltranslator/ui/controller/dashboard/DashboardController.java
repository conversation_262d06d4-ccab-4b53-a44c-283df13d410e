package com.planb.exceltranslator.ui.controller.dashboard;

import com.planb.exceltranslator.domain.model.*;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import com.planb.exceltranslator.ui.service.dashboard.*;
import com.planb.exceltranslator.ui.service.common.DialogService;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.Pane;
import javafx.scene.shape.Circle;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.io.File;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.CompletableFuture;

/**
 * Main Dashboard Controller for the Excel Translator application.
 * Follows SonarQube standards with proper separation of concerns.
 * Uses Java 8 features, Lombok, and dependency injection.
 */
@Slf4j
@Singleton
public class DashboardController implements Initializable {
    
    // FXML UI Components - File Section
    @FXML private Pane fileDragAndDrop;
    @FXML private Label selectedFile;
    @FXML private Button selectFileButton;
    @FXML private Label fileSize;
    
    // FXML UI Components - Translation Settings
    @FXML private ComboBox<Language> sourceLanguage;
    @FXML private ComboBox<Language> targetLanguage;
    @FXML private ComboBox<TranslatorType> translator;
    @FXML private Spinner<Integer> batchSize;
    
    // FXML UI Components - Progress and Status
    @FXML private ProgressBar progressBar;
    @FXML private Label progressStatus;
    @FXML private Label processCompletionStatus;
    @FXML private Circle statusIndicator;
    
    // FXML UI Components - Sheets and Logs
    @FXML private AnchorPane sheetsDetectedList;
    @FXML private ScrollPane sheetsScrollPane;
    @FXML private TextArea logArea;
    
    // FXML UI Components - Action Buttons
    @FXML private Button translateButton;
    @FXML private Button cancelButton;
    @FXML private Button exportButton;
    
    // FXML UI Components - Menu Items
    @FXML private MenuItem selectFileMenuItem;
    @FXML private MenuItem exportFileMenuItem;
    @FXML private MenuItem preferencesMenuItem;
    @FXML private MenuItem aboutMenuItem;
    @FXML private MenuItem exitMenuItem;
    
    // Injected Dependencies
    private final ConfigurationManager configManager;
    private final LanguageManager languageManager;
    private final DashboardUIInitializer uiInitializer;
    private final DashboardFileHandler fileHandler;
    private final DashboardTranslationHandler translationHandler;
    private final DashboardProgressHandler progressHandler;
    private final DashboardLanguageHandler languageHandler;
    private final DialogService dialogService;
    
    // Application State
    private File selectedExcelFile;
    private List<ExcelSheet> detectedSheets;
    private TranslationResult lastTranslationResult;
    private Language autoDetectedLanguage;
    
    @Inject
    public DashboardController(
            ConfigurationManager configManager,
            LanguageManager languageManager,
            DashboardUIInitializer uiInitializer,
            DashboardFileHandler fileHandler,
            DashboardTranslationHandler translationHandler,
            DashboardProgressHandler progressHandler,
            DashboardLanguageHandler languageHandler,
            DialogService dialogService) {
        this.configManager = configManager;
        this.languageManager = languageManager;
        this.uiInitializer = uiInitializer;
        this.fileHandler = fileHandler;
        this.translationHandler = translationHandler;
        this.progressHandler = progressHandler;
        this.languageHandler = languageHandler;
        this.dialogService = dialogService;
    }
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("Initializing Dashboard Controller...");
        
        try {
            initializeServices();
            initializeUI();
            setupEventHandlers();
            updateUIState();
            
            log.info("Dashboard Controller initialized successfully");
            
        } catch (Exception e) {
            log.error("Failed to initialize Dashboard Controller", e);
            dialogService.showError("Initialization Error", 
                "Failed to initialize the dashboard: " + e.getMessage());
        }
    }
    
    /**
     * Initializes all core services
     */
    private void initializeServices() {
        uiInitializer.initialize(
            sourceLanguage, targetLanguage, translator, batchSize,
            progressBar, progressStatus, processCompletionStatus,
            fileDragAndDrop, sheetsScrollPane, logArea, statusIndicator
        );
        
        fileHandler.initialize(this::onFileSelected, this::onFileAnalyzed);
        translationHandler.initialize(this::onTranslationProgress, this::onTranslationComplete);
        progressHandler.initialize(progressBar, progressStatus, processCompletionStatus, statusIndicator);
        languageHandler.initialize(this::onLanguageChanged);
        
        log.debug("Services initialized successfully");
    }
    
    /**
     * Initializes UI components
     */
    private void initializeUI() {
        uiInitializer.setupDragAndDrop(fileDragAndDrop, this::onFileDropped);
        uiInitializer.bindUIElementsToLanguage(getAllUIComponents());
        
        log.debug("UI components initialized successfully");
    }
    
    /**
     * Sets up event handlers for UI components
     */
    private void setupEventHandlers() {
        // File operations
        selectFileButton.setOnAction(e -> selectFile());
        selectFileMenuItem.setOnAction(e -> selectFile());
        
        // Translation operations
        translateButton.setOnAction(e -> startTranslation());
        cancelButton.setOnAction(e -> cancelTranslation());
        exportButton.setOnAction(e -> exportTranslatedFile());
        exportFileMenuItem.setOnAction(e -> exportTranslatedFile());
        
        // Menu operations
        preferencesMenuItem.setOnAction(e -> openPreferences());
        aboutMenuItem.setOnAction(e -> showAbout());
        exitMenuItem.setOnAction(e -> exitApplication());
        
        // Language and settings change handlers
        languageHandler.setupLanguageBindings(sourceLanguage, targetLanguage);
        translator.valueProperty().addListener((obs, oldVal, newVal) -> onTranslatorChange(newVal));
        batchSize.valueProperty().addListener((obs, oldVal, newVal) -> onBatchSizeChange(newVal));
        
        log.debug("Event handlers setup completed");
    }
    
    /**
     * Updates the UI state based on current application state
     */
    private void updateUIState() {
        boolean hasFile = selectedExcelFile != null;
        boolean hasSheets = detectedSheets != null && !detectedSheets.isEmpty();
        boolean hasTranslationResult = lastTranslationResult != null;
        boolean isTranslating = translationHandler.isTranslationInProgress();
        
        translateButton.setDisable(!hasSheets || isTranslating);
        cancelButton.setDisable(!isTranslating);
        exportButton.setDisable(!hasTranslationResult);
        exportFileMenuItem.setDisable(!hasTranslationResult);
        
        updateFileDisplay(hasFile);
        progressHandler.updateUIState(isTranslating);
        
        log.debug("UI state updated");
    }
    
    // Event Handler Methods
    
    private void selectFile() {
        log.debug("File selection requested");
        fileHandler.selectFile()
            .thenAccept(this::handleFileSelection);
    }
    
    private void onFileDropped(File file) {
        log.debug("File dropped: {}", file.getName());
        handleFileSelection(file);
    }
    
    private void handleFileSelection(File file) {
        if (file != null) {
            selectedExcelFile = file;
            fileHandler.analyzeFileAsync(file)
                .thenAccept(result -> {
                    if (result.isSuccess()) {
                        handleAnalysisSuccess(result);
                    } else {
                        handleAnalysisError(result.getErrorMessage());
                    }
                })
                .exceptionally(this::handleException);
            
            updateUIState();
        }
    }
    
    private void startTranslation() {
        if (!validateTranslationInputs()) {
            return;
        }
        
        TranslationRequest request = buildTranslationRequest();
        translationHandler.startTranslationAsync(request)
            .thenAccept(result -> {
                lastTranslationResult = result;
                onTranslationComplete(result);
            })
            .exceptionally(this::handleException);
        
        updateUIState();
    }
    
    private void cancelTranslation() {
        translationHandler.cancelTranslation();
        updateUIState();
    }
    
    private void exportTranslatedFile() {
        if (lastTranslationResult == null) {
            dialogService.showError("Export Error", "No translation result available for export.");
            return;
        }
        
        fileHandler.selectOutputFile(selectedExcelFile)
            .thenCompose(outputFile -> 
                outputFile != null ? 
                translationHandler.exportTranslatedFileAsync(lastTranslationResult, outputFile) :
                CompletableFuture.completedFuture(null))
            .thenRun(() -> dialogService.showInfo("Export Success", "File exported successfully"))
            .exceptionally(this::handleException);
    }
    
    // Callback Methods
    
    private void onFileSelected(File file) {
        selectedExcelFile = file;
        updateFileDisplay(true);
    }
    
    private void onFileAnalyzed(List<ExcelSheet> sheets, Language detectedLanguage) {
        detectedSheets = sheets;
        autoDetectedLanguage = detectedLanguage;
        
        if (detectedLanguage != null) {
            sourceLanguage.setValue(detectedLanguage);
        }
        
        updateUIState();
    }
    
    private void onTranslationProgress(double progress, String message) {
        progressHandler.updateProgress(progress, message);
    }
    
    private void onTranslationComplete(TranslationResult result) {
        if (result.isSuccess()) {
            dialogService.showInfo("Translation Complete", "Translation completed successfully!");
        } else {
            dialogService.showError("Translation Failed", 
                "Translation failed: " + String.join(", ", result.getErrors()));
        }
        updateUIState();
    }
    
    private void onLanguageChanged(Language source, Language target) {
        languageHandler.handleLanguageChange(source, target, targetLanguage);
    }
    
    private void onTranslatorChange(TranslatorType newTranslator) {
        if (newTranslator != null) {
            log.debug("Translator changed to: {}", newTranslator.getDisplayName());
        }
    }
    
    private void onBatchSizeChange(Integer newBatchSize) {
        if (newBatchSize != null) {
            log.debug("Batch size changed to: {}", newBatchSize);
        }
    }
    
    // Menu Operations
    
    private void openPreferences() {
        dialogService.openPreferences();
    }
    
    private void showAbout() {
        dialogService.showAbout();
    }
    
    private void exitApplication() {
        dialogService.confirmExit();
    }
    
    // Helper Methods
    
    private void handleAnalysisSuccess(FileAnalysisResult result) {
        onFileAnalyzed(result.getSheets(), result.getDetectedLanguage());
    }
    
    private void handleAnalysisError(String errorMessage) {
        dialogService.showError("Analysis Error", errorMessage);
        updateUIState();
    }
    
    private Void handleException(Throwable throwable) {
        log.error("Operation failed", throwable);
        dialogService.showError("Error", throwable.getMessage());
        updateUIState();
        return null;
    }
    
    private boolean validateTranslationInputs() {
        if (selectedExcelFile == null || detectedSheets == null || detectedSheets.isEmpty()) {
            dialogService.showError("Translation Error", "Please select a valid Excel file first.");
            return false;
        }
        
        if (targetLanguage.getValue() == null || translator.getValue() == null) {
            dialogService.showError("Translation Error", 
                "Please select target language and translator.");
            return false;
        }
        
        return true;
    }
    
    private TranslationRequest buildTranslationRequest() {
        return TranslationRequest.builder()
            .excelFile(selectedExcelFile)
            .sourceLanguage(sourceLanguage.getValue())
            .targetLanguage(targetLanguage.getValue())
            .translatorType(translator.getValue())
            .batchSize(batchSize.getValue())
            .selectedSheets(detectedSheets)
            .build();
    }
    
    private void updateFileDisplay(boolean hasFile) {
        if (hasFile && selectedExcelFile != null) {
            selectedFile.setText(selectedExcelFile.getName());
            fileSize.setText(formatFileSize(selectedExcelFile.length()));
        } else {
            selectedFile.setText("No file selected");
            fileSize.setText("");
        }
    }
    
    private String formatFileSize(long sizeInBytes) {
        if (sizeInBytes < 1024) return sizeInBytes + " B";
        int exp = (int) (Math.log(sizeInBytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", sizeInBytes / Math.pow(1024, exp), pre);
    }
    
    private UIComponents getAllUIComponents() {
        return UIComponents.builder()
            .selectedFile(selectedFile)
            .selectFileButton(selectFileButton)
            .fileSize(fileSize)
            .sourceLanguage(sourceLanguage)
            .targetLanguage(targetLanguage)
            .translator(translator)
            .translateButton(translateButton)
            .cancelButton(cancelButton)
            .exportButton(exportButton)
            .sheetsDetectedList(sheetsDetectedList)
            .logArea(logArea)
            .build();
    }
}
