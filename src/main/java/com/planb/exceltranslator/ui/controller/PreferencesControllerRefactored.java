package com.planb.exceltranslator.ui.controller;

import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.stage.Stage;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;
import java.util.ResourceBundle;
import java.util.function.Consumer;
import java.util.regex.Pattern;

/**
 * Refactored Preferences Controller - under 300 lines.
 * Uses simplified approach with inline logic for better maintainability.
 * Follows SonarQube standards with Java 8 features and Lombok.
 */
@Slf4j
public class PreferencesControllerRefactored implements Initializable {
    
    private static final Pattern DEEPL_KEY_PATTERN = Pattern.compile("^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}:fx$");
    private static final Pattern GOOGLE_KEY_PATTERN = Pattern.compile("^[A-Za-z0-9_-]{39}$");
    
    // FXML UI Components
    @FXML private ComboBox<String> interfaceLanguageComboBox;
    @FXML private PasswordField deeplApiKeyField;
    @FXML private PasswordField googleApiKeyField;
    @FXML private Label deeplApiStatusIndicator;
    @FXML private Label googleApiStatusIndicator;
    @FXML private Tab interfaceTab;
    @FXML private Tab apiKeysTab;
    @FXML private Label interfaceLanguageLabel;
    @FXML private Label languageNoteLabel;
    @FXML private Label deeplKeyLabel;
    @FXML private Label deeplInfoLabel;
    @FXML private Label googleKeyLabel;
    @FXML private Label googleInfoLabel;
    @FXML private Label securityNoteLabel;
    @FXML private Button saveButton;
    @FXML private Button cancelButton;
    
    // Core Dependencies
    private ConfigurationManager configManager;
    private LanguageManager languageManager;
    
    // Application State
    private Stage preferencesStage;
    private Consumer<String> logCallback;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("Initializing Preferences Controller...");
        
        try {
            initializeDependencies();
            setupInterfaceLanguageOptions();
            setupApiKeyValidation();
            
            log.info("Preferences Controller initialized successfully");
            
        } catch (Exception e) {
            log.error("Failed to initialize Preferences Controller", e);
            showError("Initialization Error", "Failed to initialize preferences: " + e.getMessage());
        }
    }
    
    private void initializeDependencies() {
        configManager = ConfigurationManager.getInstance();
        languageManager = LanguageManager.getInstance();
        
        log.debug("Dependencies initialized");
    }
    
    private void setupInterfaceLanguageOptions() {
        var languages = FXCollections.observableArrayList(
            "English (Default)",
            "日本語 (Japanese)", 
            "Tiếng Việt (Vietnamese)"
        );
        
        interfaceLanguageComboBox.setItems(languages);
        interfaceLanguageComboBox.setValue("English (Default)");
    }
    
    private void setupApiKeyValidation() {
        deeplApiKeyField.textProperty().addListener((obs, oldVal, newVal) -> 
            validateDeeplApiKey(newVal, deeplApiStatusIndicator));
        
        googleApiKeyField.textProperty().addListener((obs, oldVal, newVal) -> 
            validateGoogleApiKey(newVal, googleApiStatusIndicator));
    }
    
    @FXML
    private void handleSave() {
        try {
            log.info("Saving preferences...");
            logMessage("Saving preferences changes...");
            
            boolean languageChanged = saveLanguageSettings();
            boolean apiKeysUpdated = saveApiKeySettings();
            
            configManager.saveUserPreferences();
            
            if (apiKeysUpdated) {
                log.info("Translation services need refresh due to API key changes");
                logMessage("Translation services will be refreshed with new API keys");
            }
            
            showSuccessMessage();
            logFinalSaveMessage(languageChanged, apiKeysUpdated);
            closePreferencesWindow();
            
        } catch (Exception e) {
            log.error("Failed to save preferences", e);
            showError("Save Error", "Failed to save preferences: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleCancel() {
        log.info("Preferences cancelled by user");
        closePreferencesWindow();
    }
    
    @FXML
    private void handleDeeplKeyFieldFocus() {
        validateDeeplApiKey(deeplApiKeyField.getText(), deeplApiStatusIndicator);
    }

    @FXML
    private void handleGoogleKeyFieldFocus() {
        validateGoogleApiKey(googleApiKeyField.getText(), googleApiStatusIndicator);
    }
    
    // Public setters for external dependencies
    public void setPreferencesStage(Stage stage) {
        this.preferencesStage = stage;
        loadCurrentSettings();
        bindUIElementsToLanguage();
    }
    
    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
    
    // Private helper methods
    private boolean saveLanguageSettings() {
        String selectedLanguage = interfaceLanguageComboBox.getValue();
        String languageCode = getLanguageCode(selectedLanguage);
        
        String currentLanguage = configManager.getUserPreference("interface.language");
        boolean languageChanged = !languageCode.equals(currentLanguage);
        
        if (languageChanged) {
            languageManager.setLanguage(languageCode);
            configManager.setUserPreference("interface.language", languageCode);
            
            String languageName = extractLanguageName(selectedLanguage);
            logMessage("Interface language changed to: " + languageName);
            log.info("Interface language changed from '{}' to '{}'", currentLanguage, languageCode);
        }
        
        return languageChanged;
    }
    
    private String getLanguageCode(String selectedLanguage) {
        if (selectedLanguage.contains("Japanese")) {
            return "ja";
        } else if (selectedLanguage.contains("Vietnamese")) {
            return "vi";
        } else {
            return "en";
        }
    }
    
    private String extractLanguageName(String selectedLanguage) {
        return selectedLanguage.contains("(") ? 
            selectedLanguage.substring(0, selectedLanguage.indexOf('(')).trim() : 
            selectedLanguage;
    }
    
    private boolean saveApiKeySettings() {
        boolean apiKeysUpdated = false;
        
        String deeplKey = deeplApiKeyField.getText();
        if (!deeplKey.isEmpty()) {
            configManager.setUserPreference("deepl.api.key", deeplKey);
            log.info("DeepL API key updated");
            logMessage("DeepL API key updated successfully");
            apiKeysUpdated = true;
        }

        String googleKey = googleApiKeyField.getText();
        if (!googleKey.isEmpty()) {
            configManager.setUserPreference("google.api.key", googleKey);
            log.info("Google API key updated");
            logMessage("Google Translate API key updated successfully");
            apiKeysUpdated = true;
        }
        
        return apiKeysUpdated;
    }
    
    private void loadCurrentSettings() {
        // Load language setting
        String currentLanguage = configManager.getUserPreference("interface.language");
        String displayLanguage = mapLanguageCodeToDisplay(currentLanguage);
        interfaceLanguageComboBox.setValue(displayLanguage);
        
        // Load API keys (masked)
        String deeplKey = configManager.getUserPreference("deepl.api.key");
        if (deeplKey != null && !deeplKey.isEmpty()) {
            deeplApiKeyField.setPromptText("API key configured");
            updateStatusIndicator(deeplApiStatusIndicator, "✓ Configured", "green");
        }
        
        String googleKey = configManager.getUserPreference("google.api.key");
        if (googleKey != null && !googleKey.isEmpty()) {
            googleApiKeyField.setPromptText("API key configured");
            updateStatusIndicator(googleApiStatusIndicator, "✓ Configured", "green");
        }
        
        log.debug("Current settings loaded into UI");
    }
    
    private String mapLanguageCodeToDisplay(String languageCode) {
        if (languageCode == null) {
            return "English (Default)";
        }
        
        switch (languageCode) {
            case "ja":
                return "日本語 (Japanese)";
            case "vi":
                return "Tiếng Việt (Vietnamese)";
            default:
                return "English (Default)";
        }
    }
    
    private void validateDeeplApiKey(String apiKey, Label statusIndicator) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            updateStatusIndicator(statusIndicator, "Not configured", "gray");
            return;
        }
        
        if (DEEPL_KEY_PATTERN.matcher(apiKey.trim()).matches()) {
            updateStatusIndicator(statusIndicator, "✓ Valid format", "green");
        } else {
            updateStatusIndicator(statusIndicator, "✗ Invalid format", "red");
        }
    }
    
    private void validateGoogleApiKey(String apiKey, Label statusIndicator) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            updateStatusIndicator(statusIndicator, "Not configured", "gray");
            return;
        }
        
        if (GOOGLE_KEY_PATTERN.matcher(apiKey.trim()).matches()) {
            updateStatusIndicator(statusIndicator, "✓ Valid format", "green");
        } else {
            updateStatusIndicator(statusIndicator, "✗ Invalid format", "red");
        }
    }
    
    private void updateStatusIndicator(Label indicator, String text, String color) {
        indicator.setText(text);
        indicator.setStyle("-fx-text-fill: " + color + ";");
    }
    
    private void bindUIElementsToLanguage() {
        try {
            // Bind UI elements to language properties for internationalization
            // This is a simplified version - in a full implementation,
            // you would bind each UI element to the appropriate language property
            
            log.debug("UI elements bound to language manager");
            
        } catch (Exception e) {
            log.error("Failed to bind UI elements to language manager", e);
        }
    }
    
    private void showSuccessMessage() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Preferences Saved");
        alert.setHeaderText(null);
        alert.setContentText("Your preferences have been saved successfully.");
        alert.showAndWait();
    }
    
    private void showError(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText("Error");
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void logFinalSaveMessage(boolean languageChanged, boolean apiKeysUpdated) {
        String message;
        if (languageChanged && apiKeysUpdated) {
            message = "Preferences saved: Language and API keys updated successfully";
        } else if (languageChanged) {
            message = "Preferences saved: Interface language updated";
        } else if (apiKeysUpdated) {
            message = "Preferences saved: API keys updated successfully";
        } else {
            message = "Preferences saved: No changes were made";
        }
        
        log.info(message);
        logMessage(message);
    }
    
    private void logMessage(String message) {
        log.info(message);
        if (logCallback != null) {
            logCallback.accept(message);
        }
    }
    
    private void closePreferencesWindow() {
        if (preferencesStage != null) {
            preferencesStage.close();
        }
    }
}
