package com.planb.exceltranslator.ui.controller.preferences;

import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import com.planb.exceltranslator.ui.service.preferences.PreferencesUIManager;
import com.planb.exceltranslator.ui.service.preferences.PreferencesValidator;
import com.planb.exceltranslator.ui.service.preferences.PreferencesApiManager;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.stage.Stage;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;
import java.util.ResourceBundle;
import java.util.function.Consumer;

/**
 * Refactored Preferences Controller - under 300 lines.
 * Uses delegation pattern with specialized service classes.
 * Follows SonarQube standards with Java 8 features and Lombok.
 */
@Slf4j
public class PreferencesController implements Initializable {
    
    // FXML UI Components
    @FXML private ComboBox<String> interfaceLanguageComboBox;
    @FXML private PasswordField deeplApiKeyField;
    @FXML private PasswordField googleApiKeyField;
    @FXML private Label deeplApiStatusIndicator;
    @FXML private Label googleApiStatusIndicator;
    @FXML private Tab interfaceTab;
    @FXML private Tab apiKeysTab;
    @FXML private Label interfaceLanguageLabel;
    @FXML private Label languageNoteLabel;
    @FXML private Label deeplKeyLabel;
    @FXML private Label deeplInfoLabel;
    @FXML private Label googleKeyLabel;
    @FXML private Label googleInfoLabel;
    @FXML private Label securityNoteLabel;
    @FXML private Button saveButton;
    @FXML private Button cancelButton;
    
    // Core Dependencies
    private ConfigurationManager configManager;
    private LanguageManager languageManager;
    
    // Service Layer - Delegated Components
    private PreferencesUIManager uiManager;
    private PreferencesValidator validator;
    private PreferencesApiManager apiManager;
    
    // Application State
    private Stage preferencesStage;
    private Consumer<String> logCallback;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("Initializing Preferences Controller...");
        
        try {
            initializeDependencies();
            initializeServices();
            setupUIComponents();
            
            log.info("Preferences Controller initialized successfully");
            
        } catch (Exception e) {
            log.error("Failed to initialize Preferences Controller", e);
            showError("Initialization Error", "Failed to initialize preferences: " + e.getMessage());
        }
    }
    
    private void initializeDependencies() {
        configManager = ConfigurationManager.getInstance();
        languageManager = LanguageManager.getInstance();
        
        // Initialize service layer
        uiManager = new PreferencesUIManager(configManager, languageManager);
        validator = new PreferencesValidator();
        apiManager = new PreferencesApiManager(configManager);
        
        log.debug("Dependencies initialized");
    }
    
    private void initializeServices() {
        setupInterfaceLanguageOptions();
        setupApiKeyValidation();
        
        log.debug("Services initialized");
    }
    
    private void setupUIComponents() {
        uiManager.bindUIElementsToLanguage(getAllUIComponents());
        
        log.debug("UI components setup completed");
    }
    
    private void setupInterfaceLanguageOptions() {
        var languages = FXCollections.observableArrayList(
            "English (Default)",
            "日本語 (Japanese)", 
            "Tiếng Việt (Vietnamese)"
        );
        
        interfaceLanguageComboBox.setItems(languages);
        interfaceLanguageComboBox.setValue("English (Default)");
    }
    
    private void setupApiKeyValidation() {
        validator.setupApiKeyValidation(
            deeplApiKeyField, 
            googleApiKeyField,
            deeplApiStatusIndicator, 
            googleApiStatusIndicator, 
            uiManager
        );
    }
    
    @FXML
    private void handleSave() {
        try {
            log.info("Saving preferences...");
            logMessage("Saving preferences changes...");
            
            boolean languageChanged = saveLanguageSettings();
            boolean apiKeysUpdated = saveApiKeySettings();
            
            configManager.saveUserPreferences();
            
            if (apiKeysUpdated) {
                apiManager.refreshTranslationServices();
                log.info("Translation services refreshed due to API key changes");
                logMessage("Translation services refreshed with new API keys");
            }
            
            showSuccessMessage();
            logFinalSaveMessage(languageChanged, apiKeysUpdated);
            closePreferencesWindow();
            
        } catch (Exception e) {
            log.error("Failed to save preferences", e);
            showError("Save Error", "Failed to save preferences: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleCancel() {
        log.info("Preferences cancelled by user");
        closePreferencesWindow();
    }
    
    @FXML
    private void handleDeeplKeyFieldFocus() {
        validator.validateDeeplApiKey(deeplApiKeyField.getText(), deeplApiStatusIndicator);
    }

    @FXML
    private void handleGoogleKeyFieldFocus() {
        validator.validateGoogleApiKey(googleApiKeyField.getText(), googleApiStatusIndicator);
    }
    
    // Public setters for external dependencies
    public void setPreferencesStage(Stage stage) {
        this.preferencesStage = stage;
        loadCurrentSettings();
        uiManager.bindStageToLanguage(stage);
    }
    
    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
    
    // Private helper methods
    private boolean saveLanguageSettings() {
        String selectedLanguage = interfaceLanguageComboBox.getValue();
        String languageCode = getLanguageCode(selectedLanguage);
        
        String currentLanguage = configManager.getUserPreference("interface.language");
        boolean languageChanged = !languageCode.equals(currentLanguage);
        
        if (languageChanged) {
            languageManager.setLanguage(languageCode);
            configManager.setUserPreference("interface.language", languageCode);
            
            String languageName = extractLanguageName(selectedLanguage);
            logMessage("Interface language changed to: " + languageName);
            log.info("Interface language changed from '{}' to '{}'", currentLanguage, languageCode);
        }
        
        return languageChanged;
    }
    
    private String getLanguageCode(String selectedLanguage) {
        if (selectedLanguage.contains("Japanese")) {
            return "ja";
        } else if (selectedLanguage.contains("Vietnamese")) {
            return "vi";
        } else {
            return "en";
        }
    }
    
    private String extractLanguageName(String selectedLanguage) {
        return selectedLanguage.contains("(") ? 
            selectedLanguage.substring(0, selectedLanguage.indexOf('(')).trim() : 
            selectedLanguage;
    }
    
    private boolean saveApiKeySettings() {
        boolean apiKeysUpdated = false;
        
        String deeplKey = deeplApiKeyField.getText();
        if (!deeplKey.isEmpty()) {
            configManager.setUserPreference("deepl.api.key", deeplKey);
            log.info("DeepL API key updated");
            logMessage("DeepL API key updated successfully");
            apiKeysUpdated = true;
        }

        String googleKey = googleApiKeyField.getText();
        if (!googleKey.isEmpty()) {
            configManager.setUserPreference("google.api.key", googleKey);
            log.info("Google API key updated");
            logMessage("Google Translate API key updated successfully");
            apiKeysUpdated = true;
        }
        
        return apiKeysUpdated;
    }
    
    private void loadCurrentSettings() {
        uiManager.loadCurrentSettings(
            interfaceLanguageComboBox, 
            deeplApiKeyField, 
            googleApiKeyField,
            deeplApiStatusIndicator, 
            googleApiStatusIndicator
        );
    }
    
    private void showSuccessMessage() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(languageManager.getString("preferences.saved.title"));
        alert.setHeaderText(null);
        alert.setContentText(languageManager.getString("preferences.saved.message"));
        alert.showAndWait();
    }
    
    private void showError(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText("Error");
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void logFinalSaveMessage(boolean languageChanged, boolean apiKeysUpdated) {
        String message;
        if (languageChanged && apiKeysUpdated) {
            message = "Preferences saved: Language and API keys updated successfully";
        } else if (languageChanged) {
            message = "Preferences saved: Interface language updated";
        } else if (apiKeysUpdated) {
            message = "Preferences saved: API keys updated successfully";
        } else {
            message = "Preferences saved: No changes were made";
        }
        
        log.info(message);
        logMessage(message);
    }
    
    private void logMessage(String message) {
        log.info(message);
        if (logCallback != null) {
            logCallback.accept(message);
        }
    }
    
    private void closePreferencesWindow() {
        if (preferencesStage != null) {
            preferencesStage.close();
        }
    }
    
    private PreferencesUIComponents getAllUIComponents() {
        return PreferencesUIComponents.builder()
            .interfaceTab(interfaceTab)
            .apiKeysTab(apiKeysTab)
            .interfaceLanguageLabel(interfaceLanguageLabel)
            .languageNoteLabel(languageNoteLabel)
            .deeplKeyLabel(deeplKeyLabel)
            .deeplInfoLabel(deeplInfoLabel)
            .googleKeyLabel(googleKeyLabel)
            .googleInfoLabel(googleInfoLabel)
            .securityNoteLabel(securityNoteLabel)
            .saveButton(saveButton)
            .cancelButton(cancelButton)
            .interfaceLanguageComboBox(interfaceLanguageComboBox)
            .deeplApiKeyField(deeplApiKeyField)
            .googleApiKeyField(googleApiKeyField)
            .build();
    }
}
