package com.planb.exceltranslator.ui.controller.common;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * DTO for controller initialization dependencies.
 * Uses Lombok for clean code generation.
 */
@Getter
@RequiredArgsConstructor
public final class ControllerDependencies {
    private final Object configurationManager;
    private final Object languageManager;
    private final Object translationService;
    
    /**
     * Builder pattern for creating controller dependencies
     */
    public static ControllerDependenciesBuilder builder() {
        return new ControllerDependenciesBuilder();
    }
    
    public static class ControllerDependenciesBuilder {
        private Object configurationManager;
        private Object languageManager;
        private Object translationService;
        
        public ControllerDependenciesBuilder configurationManager(Object configurationManager) {
            this.configurationManager = configurationManager;
            return this;
        }
        
        public ControllerDependenciesBuilder languageManager(Object languageManager) {
            this.languageManager = languageManager;
            return this;
        }
        
        public ControllerDependenciesBuilder translationService(Object translationService) {
            this.translationService = translationService;
            return this;
        }
        
        public ControllerDependencies build() {
            return new ControllerDependencies(configurationManager, languageManager, translationService);
        }
    }
}
