package com.planb.exceltranslator.ui.controller.dashboard;

import com.planb.exceltranslator.domain.model.Language;
import com.planb.exceltranslator.domain.model.TranslatorType;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.stage.Stage;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * Coordinates events and handlers for the Dashboard.
 * Follows SonarQube standards and uses Java 8 features with Lombok.
 */
@Slf4j
@RequiredArgsConstructor
public class DashboardEventCoordinator {
    
    private final DashboardFileHandler fileHandler;
    private final DashboardTranslationHandler translationHandler;
    private final LanguageManager languageManager;
    
    private Consumer<String> logCallback;
    private EventCallbacks callbacks;
    
    /**
     * Container for event callbacks using Lombok builder pattern
     */
    @Getter
    @Builder
    public static class EventCallbacks {
        private final Consumer<File> onFileSelected;
        private final Consumer<Language> onLanguageDetected;
        private final Consumer<Boolean> onTranslationStateChange;
        private final Runnable onTranslationComplete;
        private final Consumer<Double> onProgressUpdate;
    }
    
    /**
     * Initializes the event coordinator with callbacks
     * @param callbacks Event callbacks configuration
     */
    public void initialize(EventCallbacks callbacks) {
        this.callbacks = callbacks;
        
        // Configure file handler
        fileHandler.setLogCallback(this::logMessage);
        fileHandler.setFileSelectionCallback(this::handleFileSelected);
        
        // Configure translation handler
        translationHandler.setLogCallback(this::logMessage);
        translationHandler.setProgressCallback(this::handleProgressUpdate);
        translationHandler.setTranslationStateCallback(this::handleTranslationStateChange);
        translationHandler.setOnTranslationComplete(this::handleTranslationComplete);
        
        log.debug("DashboardEventCoordinator initialized");
    }
    
    /**
     * Sets the main log callback
     * @param logCallback Callback for log messages
     */
    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
    
    /**
     * Handles file selection events
     * @param ownerStage The parent stage for dialogs
     */
    public void handleFileSelection(Stage ownerStage) {
        fileHandler.handleFileSelection(ownerStage);
    }
    
    /**
     * Handles file drop events
     * @param file The dropped file
     */
    public void handleFileDrop(File file) {
        fileHandler.handleFileDropped(file);
    }
    
    /**
     * Starts translation process
     * @param parameters Translation parameters
     */
    public void startTranslation(TranslationParameters parameters) {
        if (translationHandler.isTranslating()) {
            logMessage("Translation already in progress");
            return;
        }
        
        translationHandler.startTranslation(
            parameters.getSourceLanguage(),
            parameters.getTargetLanguage(),
            parameters.getTranslatorType(),
            parameters.getBatchSize()
        );
    }
    
    /**
     * Cancels current translation
     */
    public void cancelTranslation() {
        translationHandler.cancelTranslation();
    }
    
    /**
     * Checks if translation is in progress
     * @return true if translating, false otherwise
     */
    public boolean isTranslating() {
        return translationHandler.isTranslating();
    }
    
    /**
     * Handles file selection callback
     * @param file Selected file
     */
    private void handleFileSelected(File file) {
        Optional.ofNullable(callbacks)
                .map(EventCallbacks::getOnFileSelected)
                .ifPresent(callback -> callback.accept(file));
    }
    
    /**
     * Handles language detection callback
     * @param language Detected language
     */
    private void handleLanguageDetected(Language language) {
        Optional.ofNullable(callbacks)
                .map(EventCallbacks::getOnLanguageDetected)
                .ifPresent(callback -> callback.accept(language));
    }
    
    /**
     * Handles translation state change callback
     * @param isTranslating New translation state
     */
    private void handleTranslationStateChange(Boolean isTranslating) {
        Optional.ofNullable(callbacks)
                .map(EventCallbacks::getOnTranslationStateChange)
                .ifPresent(callback -> callback.accept(isTranslating));
    }
    
    /**
     * Handles translation completion callback
     */
    private void handleTranslationComplete() {
        Optional.ofNullable(callbacks)
                .map(EventCallbacks::getOnTranslationComplete)
                .ifPresent(Runnable::run);
    }
    
    /**
     * Handles progress update callback
     * @param progress Progress value (0.0 to 1.0)
     */
    private void handleProgressUpdate(Double progress) {
        Optional.ofNullable(callbacks)
                .map(EventCallbacks::getOnProgressUpdate)
                .ifPresent(callback -> callback.accept(progress));
    }
    
    /**
     * Logs a message using the configured callback
     * @param message The message to log
     */
    private void logMessage(String message) {
        Optional.ofNullable(logCallback)
                .ifPresent(callback -> callback.accept(message));
    }
    
    /**
     * Translation parameters DTO using Lombok
     */
    @Getter
    @Builder
    public static class TranslationParameters {
        private final Language sourceLanguage;
        private final Language targetLanguage;
        private final TranslatorType translatorType;
        private final int batchSize;
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        translationHandler.cleanup();
        log.debug("DashboardEventCoordinator cleanup completed");
    }
}
