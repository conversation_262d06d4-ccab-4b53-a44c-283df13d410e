package com.planb.exceltranslator.ui.controller;

import com.planb.exceltranslator.application.FileAnalysisService;
import com.planb.exceltranslator.application.LanguageDetectionService;
import com.planb.exceltranslator.application.TranslationApplicationService;
import com.planb.exceltranslator.domain.model.*;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import com.planb.exceltranslator.ui.component.UIControlsInitializer;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.MouseEvent;
import javafx.stage.FileChooser;
import javafx.stage.Stage;

import java.io.File;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Final compact version of DashboardController under 300 lines
 * Modern Java practices with inline service methods
 */
public class DashboardControllerFinal implements Initializable {
    private static final System.Logger logger = System.getLogger(DashboardControllerFinal.class.getName());
    
    // FXML UI Components
    @FXML private Button selectFileButton, translateButton, exportButton, detectLanguageButton;
    @FXML private ComboBox<String> sourceLanguageComboBox, targetLanguageComboBox, translatorTypeComboBox;
    @FXML private TableView<ExcelSheet> sheetsTableView;
    @FXML private TableColumn<ExcelSheet, String> sheetNameColumn, sheetRowsColumn;
    @FXML private CheckBox selectAllSheetsCheckBox;
    @FXML private TextArea logArea;
    @FXML private ProgressBar progressBar;
    @FXML private Label progressStatus, statusIndicator, processCompletionStatus;

    // Service Dependencies
    private final ConfigurationManager configManager;
    private final LanguageManager languageManager;
    private final FileAnalysisService fileAnalysisService;
    private final LanguageDetectionService languageDetectionService;
    private final TranslationApplicationService translationService;
    
    // State
    private File selectedFile;
    private List<ExcelSheet> availableSheets = new ArrayList<>();

    public DashboardControllerFinal() {
        // Initialize core services
        this.configManager = ConfigurationManager.getInstance();
        this.languageManager = LanguageManager.getInstance();
        this.fileAnalysisService = new FileAnalysisService();
        this.languageDetectionService = new LanguageDetectionService();
        this.translationService = new TranslationApplicationService();
        
        logger.log(System.Logger.Level.INFO, "DashboardController initialized");
    }

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            logger.log(System.Logger.Level.INFO, "Initializing Dashboard UI components");
            initializeControls();
            setupEventHandlers();
            updateUIState();
            logger.log(System.Logger.Level.INFO, "Dashboard initialization completed successfully");
        } catch (Exception e) {
            logger.log(System.Logger.Level.ERROR, "Failed to initialize Dashboard", e);
            showError("Failed to initialize Dashboard: " + e.getMessage());
        }
    }

    private void initializeControls() {
        UIControlsInitializer.initializeLanguageComboBoxes(sourceLanguageComboBox, targetLanguageComboBox);
        UIControlsInitializer.initializeTranslatorTypeComboBox(translatorTypeComboBox);
        sheetsService.initializeTableView(sheetsTableView, sheetNameColumn, sheetRowsColumn);
        progressService.initializeProgressComponents(progressBar, progressStatus, processCompletionStatus);
    }

    private void setupEventHandlers() {
        selectFileButton.setOnAction(e -> selectFile());
        translateButton.setOnAction(e -> startTranslation());
        exportButton.setOnAction(e -> exportResults());
        detectLanguageButton.setOnAction(e -> detectLanguage());
        selectAllSheetsCheckBox.setOnAction(e -> toggleAllSheets());
        sheetsTableView.setOnMouseClicked(this::handleSheetSelection);
    }

    @FXML
    private void selectFile() {
        logger.log(System.Logger.Level.INFO, "File selection initiated");
        
        var fileChooser = new FileChooser();
        fileChooser.setTitle("Select Excel File");
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("Excel Files", "*.xlsx", "*.xls"),
            new FileChooser.ExtensionFilter("All Files", "*.*")
        );

        var stage = (Stage) selectFileButton.getScene().getWindow();
        var file = fileChooser.showOpenDialog(stage);
        
        if (file != null) {
            processSelectedFile(file);
        }
    }

    private void processSelectedFile(File file) {
        selectedFile = file;
        statusIndicator.setText("Selected: " + file.getName());
        logger.log(System.Logger.Level.INFO, "File selected: " + file.getAbsolutePath());

        CompletableFuture.supplyAsync(() -> analyzeFile(file))
            .thenAcceptAsync(this::handleFileAnalysisResult, Platform::runLater)
            .exceptionally(this::handleFileAnalysisError);
    }

    private List<ExcelSheet> analyzeFile(File file) {
        try {
            return fileAnalysisService.analyzeFile(file).getSheets();
        } catch (Exception e) {
            logger.log(System.Logger.Level.ERROR, "File analysis failed", e);
            throw new RuntimeException("Failed to analyze file: " + e.getMessage(), e);
        }
    }

    private void handleFileAnalysisResult(List<ExcelSheet> sheets) {
        availableSheets = sheets;
        sheetsService.populateSheetTable(sheetsTableView, sheets);
        updateUIState();
        logArea.appendText("File analyzed successfully. Found " + sheets.size() + " sheets.\n");
    }

    private Void handleFileAnalysisError(Throwable throwable) {
        Platform.runLater(() -> {
            logger.log(System.Logger.Level.ERROR, "File analysis error", throwable);
            showError("File analysis failed: " + throwable.getMessage());
        });
        return null;
    }

    @FXML
    private void detectLanguage() {
        if (!validateFileSelection()) return;
        
        logger.log(System.Logger.Level.INFO, "Starting language detection");
        
        CompletableFuture.supplyAsync(() -> performLanguageDetection())
            .thenAcceptAsync(this::handleLanguageDetectionResult, Platform::runLater)
            .exceptionally(this::handleLanguageDetectionError);
    }

    private LanguageDetectionService.LanguageDetectionResult performLanguageDetection() {
        var selectedSheets = getSelectedSheets();
        return languageDetectionService.detectLanguageFromSheets(selectedSheets);
    }

    private void handleLanguageDetectionResult(LanguageDetectionService.LanguageDetectionResult result) {
        if (result.getDetectionStatus() == LanguageDetectionService.LanguageDetectionResult.DetectionStatus.SUCCESS) {
            var suggestion = result.getSuggestion();
            sourceLanguageComboBox.setValue(suggestion.getSuggestedLanguage().name());
            logArea.appendText("Language detected: " + suggestion.getSuggestedLanguage().name() + 
                             " (confidence: " + String.format("%.2f", suggestion.getConfidence()) + ")\n");
        } else {
            logArea.appendText("Language detection failed: " + result.getErrorMessage() + "\n");
        }
    }

    private Void handleLanguageDetectionError(Throwable throwable) {
        Platform.runLater(() -> {
            logger.log(System.Logger.Level.ERROR, "Language detection error", throwable);
            showError("Language detection failed: " + throwable.getMessage());
        });
        return null;
    }

    @FXML
    private void startTranslation() {
        if (!validateTranslationInputs()) return;
        
        logger.log(System.Logger.Level.INFO, "Starting translation process");
        
        var task = createTranslationTask();
        progressService.bindProgressToTask(progressBar, progressStatus, task);
        
        task.setOnSucceeded(e -> handleTranslationSuccess(task.getValue()));
        task.setOnFailed(e -> handleTranslationFailure(task.getException()));
        
        new Thread(task).start();
        updateUIState();
    }

    private Task<TranslationResult> createTranslationTask() {
        return new Task<>() {
            @Override
            protected TranslationResult call() throws Exception {
                var request = buildTranslationRequest();
                return translationService.translateFile(request);
            }
        };
    }

    private TranslationRequest buildTranslationRequest() {
        return TranslationRequest.builder()
            .file(selectedFile)
            .sheets(getSelectedSheets())
            .sourceLanguage(Language.valueOf(sourceLanguageComboBox.getValue()))
            .targetLanguage(Language.valueOf(targetLanguageComboBox.getValue()))
            .translatorType(TranslatorType.valueOf(translatorTypeComboBox.getValue()))
            .build();
    }

    private void handleTranslationSuccess(TranslationResult result) {
        Platform.runLater(() -> {
            logger.log(System.Logger.Level.INFO, "Translation completed successfully");
            logArea.appendText("Translation completed successfully!\n");
            processCompletionStatus.setText("Translation completed");
            updateUIState();
        });
    }

    private void handleTranslationFailure(Throwable exception) {
        Platform.runLater(() -> {
            logger.log(System.Logger.Level.ERROR, "Translation failed", exception);
            showError("Translation failed: " + exception.getMessage());
            processCompletionStatus.setText("Translation failed");
            updateUIState();
        });
    }

    @FXML
    private void exportResults() {
        // Implementation for export functionality
        logger.log(System.Logger.Level.INFO, "Export results requested");
        showInfo("Export functionality will be implemented based on translation results");
    }

    private void toggleAllSheets() {
        boolean selectAll = selectAllSheetsCheckBox.isSelected();
        sheetsTableView.getItems().forEach(sheet -> sheet.setSelected(selectAll));
        sheetsTableView.refresh();
    }

    private void handleSheetSelection(MouseEvent event) {
        if (event.getClickCount() == 2) {
            var selectedSheet = sheetsTableView.getSelectionModel().getSelectedItem();
            if (selectedSheet != null) {
                selectedSheet.setSelected(!selectedSheet.isSelected());
                sheetsTableView.refresh();
            }
        }
    }

    private boolean validateFileSelection() {
        if (selectedFile == null) {
            showError("Please select an Excel file first");
            return false;
        }
        return true;
    }

    private boolean validateTranslationInputs() {
        return validateFileSelection() && 
               validateLanguageSelection() && 
               validateSheetSelection() && 
               validateTranslatorConfiguration();
    }

    private boolean validateLanguageSelection() {
        if (sourceLanguageComboBox.getValue() == null || targetLanguageComboBox.getValue() == null) {
            showError("Please select both source and target languages");
            return false;
        }
        return true;
    }

    private boolean validateSheetSelection() {
        if (getSelectedSheets().isEmpty()) {
            showError("Please select at least one sheet to translate");
            return false;
        }
        return true;
    }

    private boolean validateTranslatorConfiguration() {
        if (translatorTypeComboBox.getValue() == null) {
            showError("Please select a translator type");
            return false;
        }
        return translationControlService.validateTranslatorConfiguration(
            TranslatorType.valueOf(translatorTypeComboBox.getValue())
        );
    }

    private List<ExcelSheet> getSelectedSheets() {
        return availableSheets.stream()
            .filter(ExcelSheet::isSelected)
            .collect(Collectors.toList());
    }

    private void updateUIState() {
        boolean fileSelected = selectedFile != null;
        boolean hasSheets = !availableSheets.isEmpty();
        boolean hasSelectedSheets = !getSelectedSheets().isEmpty();
        
        detectLanguageButton.setDisable(!hasSelectedSheets);
        translateButton.setDisable(!hasSelectedSheets || !validateLanguageSelection());
        exportButton.setDisable(true); // Enable after successful translation
    }

    private void showError(String message) {
        var alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("Error");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
        logArea.appendText("ERROR: " + message + "\n");
    }

    private void showInfo(String message) {
        var alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Information");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
        logArea.appendText("INFO: " + message + "\n");
    }
}
