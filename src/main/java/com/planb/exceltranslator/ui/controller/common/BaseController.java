package com.planb.exceltranslator.ui.controller.common;

import javafx.fxml.Initializable;
import org.slf4j.Logger;

/**
 * Base interface for all controllers providing common functionality.
 * Follows SonarQube standards for proper abstraction and logging.
 */
public interface BaseController extends Initializable {
    
    /**
     * Gets the logger instance for the controller
     * @return Logger instance
     */
    Logger getLogger();
    
    /**
     * Performs cleanup operations when the controller is closed
     */
    default void cleanup() {
        getLogger().debug("Performing cleanup for controller: {}", this.getClass().getSimpleName());
    }
    
    /**
     * Handles initialization errors consistently across controllers
     * @param error The error that occurred during initialization
     */
    default void handleInitializationError(Exception error) {
        getLogger().error("Initialization failed for controller: {}", this.getClass().getSimpleName(), error);
    }
}
