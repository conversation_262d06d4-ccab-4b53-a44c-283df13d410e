package com.planb.exceltranslator.ui.component;

import javafx.scene.control.ProgressBar;
import javafx.scene.control.TextArea;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.shape.Circle;
import lombok.Builder;
import lombok.Getter;

/**
 * Container for UI components needed by services
 */
@Getter
@Builder
public class UIServiceComponents {
    private final TextArea logArea;
    private final Circle statusIndicator;
    private final ProgressBar progressBar;
    private final Label progressStatus;
    private final Label processCompletionStatus;
    private final Button translateButton;
    private final Button exportButton;
}
