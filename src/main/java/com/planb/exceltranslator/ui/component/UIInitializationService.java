package com.planb.exceltranslator.ui.component;

import com.planb.exceltranslator.domain.model.Language;
import com.planb.exceltranslator.domain.model.TranslatorType;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import javafx.scene.control.*;
import javafx.scene.input.DragEvent;
import javafx.scene.input.Dragboard;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.Pane;
import javafx.util.StringConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.function.Consumer;

/**
 * Service responsible for initializing UI components with proper configurations.
 * Handles setup of ComboBoxes, Spinners, Progress components, and Drag & Drop functionality.
 */
public class UIInitializationService {
    
    private static final Logger logger = LoggerFactory.getLogger(UIInitializationService.class);
    
    private final ConfigurationManager configManager;
    
    public UIInitializationService(ConfigurationManager configManager) {
        this.configManager = configManager;
        logger.debug("UIInitializationService created");
    }
    
    /**
     * Initializes language and translator combo boxes
     */
    public void initializeComboBoxes(ComboBox<Language> sourceLanguage, 
                                   ComboBox<Language> targetLanguage, 
                                   ComboBox<TranslatorType> translator) {
        logger.debug("Initializing combo boxes");
        
        // Initialize language combo boxes
        setupLanguageComboBox(sourceLanguage, "Select source language");
        setupLanguageComboBox(targetLanguage, "Select target language");
        
        // Initialize translator combo box
        setupTranslatorComboBox(translator);
        
        // Set default values
        setDefaultComboBoxValues(sourceLanguage, targetLanguage, translator);
        
        logger.debug("Combo boxes initialized successfully");
    }
    
    /**
     * Initializes the batch size spinner
     */
    public void initializeSpinner(Spinner<Integer> batchSize) {
        logger.debug("Initializing batch size spinner");
        
        int defaultBatchSize = configManager.getDefaultBatchSize();
        int maxBatchSize = configManager.getMaxBatchSize();
        
        SpinnerValueFactory<Integer> valueFactory = 
            new SpinnerValueFactory.IntegerSpinnerValueFactory(1, maxBatchSize, defaultBatchSize);
        
        batchSize.setValueFactory(valueFactory);
        batchSize.setEditable(true);
        
        // Format spinner display
        batchSize.getEditor().setTextFormatter(new TextFormatter<>(change -> {
            String newText = change.getControlNewText();
            if (newText.matches("\\d*")) {
                return change;
            }
            return null;
        }));
        
        logger.debug("Batch size spinner initialized with default value: {}", defaultBatchSize);
    }
    
    /**
     * Initializes progress bar and related status components
     */
    public void initializeProgressComponents(ProgressBar progressBar, 
                                           Label progressStatus, 
                                           Label processCompletionStatus) {
        logger.debug("Initializing progress components");
        
        progressBar.setProgress(0.0);
        progressBar.setVisible(false);
        
        progressStatus.setText("");
        processCompletionStatus.setText("");
        
        logger.debug("Progress components initialized");
    }
    
    /**
     * Initializes drag and drop functionality for file selection
     */
    public void initializeDragAndDrop(Pane dragAndDropPane, Consumer<File> fileDropHandler) {
        logger.debug("Initializing drag and drop functionality");
        
        dragAndDropPane.setOnDragOver(event -> handleDragOver(event));
        dragAndDropPane.setOnDragDropped(event -> handleDragDropped(event, fileDropHandler));
        
        // Add visual feedback
        dragAndDropPane.setOnDragEntered(event -> {
            dragAndDropPane.setStyle("-fx-background-color: #e8f4f8; -fx-border-color: #2196F3; -fx-border-width: 2px;");
            event.consume();
        });
        
        dragAndDropPane.setOnDragExited(event -> {
            dragAndDropPane.setStyle("-fx-background-color: #f5f5f5; -fx-border-color: #cccccc; -fx-border-width: 1px;");
            event.consume();
        });
        
        logger.debug("Drag and drop functionality initialized");
    }
    
    /**
     * Initializes scroll pane for sheets display
     */
    public void initializeScrollPane(ScrollPane sheetsScrollPane) {
        logger.debug("Initializing sheets scroll pane");
        
        sheetsScrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        sheetsScrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        sheetsScrollPane.setFitToWidth(true);
        
        logger.debug("Sheets scroll pane initialized");
    }
    
    // Private helper methods
    
    private void setupLanguageComboBox(ComboBox<Language> comboBox, String promptText) {
        comboBox.getItems().addAll(Language.values());
        comboBox.setPromptText(promptText);
        
        comboBox.setConverter(new StringConverter<Language>() {
            @Override
            public String toString(Language language) {
                return language != null ? language.getDisplayName() : "";
            }
            
            @Override
            public Language fromString(String string) {
                return Language.fromDisplayName(string);
            }
        });
        
        comboBox.setCellFactory(listView -> new ListCell<Language>() {
            @Override
            protected void updateItem(Language language, boolean empty) {
                super.updateItem(language, empty);
                setText(empty || language == null ? null : language.getDisplayName());
            }
        });
    }
    
    private void setupTranslatorComboBox(ComboBox<TranslatorType> translator) {
        translator.getItems().addAll(TranslatorType.values());
        translator.setPromptText("Select translator");
        
        translator.setConverter(new StringConverter<TranslatorType>() {
            @Override
            public String toString(TranslatorType translatorType) {
                return translatorType != null ? translatorType.getDisplayName() : "";
            }
            
            @Override
            public TranslatorType fromString(String string) {
                return TranslatorType.fromDisplayName(string);
            }
        });
        
        translator.setCellFactory(listView -> new ListCell<TranslatorType>() {
            @Override
            protected void updateItem(TranslatorType translatorType, boolean empty) {
                super.updateItem(translatorType, empty);
                setText(empty || translatorType == null ? null : translatorType.getDisplayName());
            }
        });
    }
    
    private void setDefaultComboBoxValues(ComboBox<Language> sourceLanguage, 
                                        ComboBox<Language> targetLanguage, 
                                        ComboBox<TranslatorType> translator) {
        // Set default source language to Auto-detect
        sourceLanguage.setValue(Language.AUTO_DETECT);
        
        // Set default target language to English
        targetLanguage.setValue(Language.ENGLISH);
        
        // Set default translator based on available API keys
        if (configManager.getDeepLApiKey().isPresent()) {
            translator.setValue(TranslatorType.DEEPL);
        } else if (configManager.getGoogleApiKey().isPresent()) {
            translator.setValue(TranslatorType.GOOGLE);
        }
        
        logger.debug("Default combo box values set");
    }
    
    private void handleDragOver(DragEvent event) {
        if (event.getGestureSource() != event.getSource() && event.getDragboard().hasFiles()) {
            event.acceptTransferModes(TransferMode.COPY_OR_MOVE);
        }
        event.consume();
    }
    
    private void handleDragDropped(DragEvent event, Consumer<File> fileDropHandler) {
        Dragboard dragboard = event.getDragboard();
        boolean success = false;
        
        if (dragboard.hasFiles()) {
            for (File file : dragboard.getFiles()) {
                if (isExcelFile(file)) {
                    fileDropHandler.accept(file);
                    success = true;
                    break;
                }
            }
        }
        
        event.setDropCompleted(success);
        event.consume();
    }
    
    private boolean isExcelFile(File file) {
        String name = file.getName().toLowerCase();
        return name.endsWith(".xlsx") || name.endsWith(".xls");
    }
}
