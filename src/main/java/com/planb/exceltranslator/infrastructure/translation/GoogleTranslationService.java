package com.planb.exceltranslator.infrastructure.translation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.planb.exceltranslator.domain.model.Language;
import com.planb.exceltranslator.domain.model.TranslatorType;
import com.planb.exceltranslator.domain.port.TranslationException;
import com.planb.exceltranslator.domain.port.TranslationService;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Google Translate API service implementation
 */
public class GoogleTranslationService implements TranslationService {

    private static final Logger logger = LoggerFactory.getLogger(GoogleTranslationService.class);
    private static final String GOOGLE_TRANSLATE_URL = "https://translation.googleapis.com/language/translate/v2";
    private static final String GOOGLE_DETECT_URL = "https://translation.googleapis.com/language/translate/v2/detect";
    private static final int MAX_BATCH_SIZE = 128; // Google allows more texts per request
    private static final int RATE_LIMIT = 10; // requests per second for paid plans

    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final String apiKey;
    private final ConfigurationManager configManager;

    public GoogleTranslationService(ConfigurationManager configManager) {
        this.configManager = configManager;
        this.apiKey = configManager.getGoogleApiKeyFromPreferences()
                .orElseThrow(() -> new IllegalStateException("Google API key not configured"));

        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();

        this.objectMapper = new ObjectMapper();

        logger.info("Google Translation Service initialized");
    }

    @Override
    public TranslatorType getTranslatorType() {
        return TranslatorType.GOOGLE;
    }

    @Override
    public String translate(String text, Language sourceLanguage, Language targetLanguage)
            throws TranslationException {

        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        List<String> results = translateBatch(Arrays.asList(text), sourceLanguage, targetLanguage);
        return results.isEmpty() ? text : results.get(0);
    }

    @Override
    public List<String> translateBatch(List<String> texts, Language sourceLanguage, Language targetLanguage)
            throws TranslationException {

        if (texts == null || texts.isEmpty()) {
            return new ArrayList<>();
        }

        validateLanguagePair(sourceLanguage, targetLanguage);

        try {
            FormBody.Builder formBuilder = new FormBody.Builder()
                    .add("key", apiKey)
                    .add("target", mapLanguageToGoogle(targetLanguage));

            // Add source language if not auto-detect
            if (sourceLanguage != Language.AUTO_DETECT) {
                formBuilder.add("source", mapLanguageToGoogle(sourceLanguage));
            }

            // Add all texts as 'q' parameters
            for (String text : texts) {
                if (text != null && !text.trim().isEmpty()) {
                    formBuilder.add("q", text);
                }
            }

            RequestBody formBody = formBuilder.build();

            Request request = new Request.Builder()
                    .url(GOOGLE_TRANSLATE_URL)
                    .post(formBody)
                    .addHeader("User-Agent", "ExcelTranslator/1.0")
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    handleErrorResponse(response);
                }

                String responseBody = response.body().string();
                return parseTranslationResponse(responseBody);
            }

        } catch (IOException e) {
            logger.error("Network error during Google translation", e);
            throw new TranslationException("Network error during translation",
                    TranslationException.ErrorType.NETWORK_ERROR,
                    "Unable to connect to Google Translate service. Please check your internet connection.", e);
        }
    }

    @Override
    public CompletableFuture<List<String>> translateBatchAsync(List<String> texts,
            Language sourceLanguage,
            Language targetLanguage) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return translateBatch(texts, sourceLanguage, targetLanguage);
            } catch (TranslationException e) {
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public Language detectLanguage(String text) throws TranslationException {
        if (text == null || text.trim().isEmpty()) {
            return Language.AUTO_DETECT;
        }

        try {
            FormBody formBody = new FormBody.Builder()
                    .add("key", apiKey)
                    .add("q", text)
                    .build();

            Request request = new Request.Builder()
                    .url(GOOGLE_DETECT_URL)
                    .post(formBody)
                    .addHeader("User-Agent", "ExcelTranslator/1.0")
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    handleErrorResponse(response);
                }

                String responseBody = response.body().string();
                return parseDetectionResponse(responseBody);
            }

        } catch (IOException e) {
            logger.error("Network error during language detection", e);
            throw new TranslationException("Network error during language detection",
                    TranslationException.ErrorType.NETWORK_ERROR,
                    "Unable to connect to Google Translate service for language detection.", e);
        }
    }

    @Override
    public boolean isAvailable() {
        try {
            healthCheck();
            return true;
        } catch (TranslationException e) {
            return false;
        }
    }

    @Override
    public int getMaxBatchSize() {
        return MAX_BATCH_SIZE;
    }

    @Override
    public int getRateLimit() {
        return RATE_LIMIT;
    }

    @Override
    public List<Language> getSupportedSourceLanguages() {
        return Arrays.asList(Language.AUTO_DETECT, Language.ENGLISH, Language.JAPANESE, Language.VIETNAMESE);
    }

    @Override
    public List<Language> getSupportedTargetLanguages() {
        return Arrays.asList(Language.ENGLISH, Language.JAPANESE, Language.VIETNAMESE);
    }

    @Override
    public boolean isLanguagePairSupported(Language sourceLanguage, Language targetLanguage) {
        return getSupportedSourceLanguages().contains(sourceLanguage) &&
                getSupportedTargetLanguages().contains(targetLanguage);
    }

    @Override
    public void healthCheck() throws TranslationException {
        try {
            // Test with a simple translation
            translate("Hello", Language.ENGLISH, Language.JAPANESE);
            logger.debug("Google Translate health check passed");
        } catch (Exception e) {
            logger.error("Google Translate health check failed", e);
            throw new TranslationException("Google Translate service health check failed",
                    TranslationException.ErrorType.SERVICE_UNAVAILABLE,
                    "Google Translate service is not available", e);
        }
    }

    /**
     * Maps internal Language enum to Google Translate language codes
     */
    private String mapLanguageToGoogle(Language language) {
        switch (language) {
            case ENGLISH:
                return "en";
            case JAPANESE:
                return "ja";
            case VIETNAMESE:
                return "vi";
            default:
                throw new IllegalArgumentException("Unsupported language for Google Translate: " + language);
        }
    }

    /**
     * Maps Google language codes back to internal Language enum
     */
    private Language mapGoogleToLanguage(String googleCode) {
        switch (googleCode.toLowerCase()) {
            case "en":
                return Language.ENGLISH;
            case "ja":
                return Language.JAPANESE;
            case "vi":
                return Language.VIETNAMESE;
            default:
                return Language.AUTO_DETECT;
        }
    }

    /**
     * Validates that the language pair is supported
     */
    private void validateLanguagePair(Language sourceLanguage, Language targetLanguage)
            throws TranslationException {
        if (!isLanguagePairSupported(sourceLanguage, targetLanguage)) {
            throw new TranslationException(
                    String.format("Language pair %s -> %s not supported by Google Translate",
                            sourceLanguage, targetLanguage),
                    TranslationException.ErrorType.UNSUPPORTED_LANGUAGE,
                    String.format("The language combination %s to %s is not supported by Google Translator",
                            sourceLanguage.getEnglishName(), targetLanguage.getEnglishName()));
        }
    }

    /**
     * Handles error responses from Google Translate API
     */
    private void handleErrorResponse(Response response) throws TranslationException {
        int code = response.code();
        String message = "Google Translate API error: " + code;

        try {
            String body = response.body().string();
            JsonNode errorNode = objectMapper.readTree(body);
            if (errorNode.has("error") && errorNode.get("error").has("message")) {
                message = errorNode.get("error").get("message").asText();
            }
        } catch (Exception e) {
            logger.debug("Failed to parse error response", e);
        }

        TranslationException.ErrorType errorType;
        String userMessage;

        switch (code) {
            case 400:
                errorType = TranslationException.ErrorType.INVALID_REQUEST;
                userMessage = "Invalid translation request. Please check your input.";
                break;
            case 403:
                errorType = TranslationException.ErrorType.API_KEY_INVALID;
                userMessage = "Google API key is invalid or access is forbidden. Please check your configuration.";
                break;
            case 429:
                errorType = TranslationException.ErrorType.RATE_LIMIT_EXCEEDED;
                userMessage = "Google Translate rate limit exceeded. Please wait and try again.";
                break;
            default:
                errorType = TranslationException.ErrorType.SERVICE_UNAVAILABLE;
                userMessage = "Google Translate service is temporarily unavailable. Please try again later.";
                break;
        }

        throw new TranslationException(message, errorType, userMessage);
    }

    /**
     * Parses the translation response from Google Translate
     */
    private List<String> parseTranslationResponse(String responseBody) throws TranslationException {
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            JsonNode data = root.get("data");
            JsonNode translations = data.get("translations");

            List<String> results = new ArrayList<>();
            if (translations.isArray()) {
                for (JsonNode translation : translations) {
                    String translatedText = translation.get("translatedText").asText();
                    results.add(translatedText);
                }
            }

            return results;

        } catch (Exception e) {
            logger.error("Failed to parse Google Translate response: {}", responseBody, e);
            throw new TranslationException("Failed to parse translation response",
                    TranslationException.ErrorType.GENERAL,
                    "Received invalid response from Google Translate service", e);
        }
    }

    /**
     * Parses the language detection response from Google Translate
     */
    private Language parseDetectionResponse(String responseBody) throws TranslationException {
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            JsonNode data = root.get("data");
            JsonNode detections = data.get("detections");

            if (detections.isArray() && detections.size() > 0) {
                JsonNode firstDetection = detections.get(0);
                if (firstDetection.isArray() && firstDetection.size() > 0) {
                    String detectedLanguage = firstDetection.get(0).get("language").asText();
                    return mapGoogleToLanguage(detectedLanguage);
                }
            }

            return Language.AUTO_DETECT;

        } catch (Exception e) {
            logger.error("Failed to parse Google language detection response: {}", responseBody, e);
            return Language.AUTO_DETECT;
        }
    }
}
