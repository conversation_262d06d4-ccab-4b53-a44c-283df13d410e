package com.planb.exceltranslator.infrastructure.translation;

import com.planb.exceltranslator.domain.model.TranslatorType;
import com.planb.exceltranslator.domain.port.TranslationService;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Factory for creating translation service instances
 * Implements the Factory pattern for translation services
 */
public class TranslationServiceFactory {

    private static final Logger logger = LoggerFactory.getLogger(TranslationServiceFactory.class);

    private final ConfigurationManager configManager;
    private final Map<TranslatorType, TranslationService> serviceCache;

    public TranslationServiceFactory(ConfigurationManager configManager) {
        this.configManager = configManager;
        this.serviceCache = new HashMap<>();

        logger.info("Translation Service Factory initialized");
    }

    /**
     * Creates or retrieves a translation service for the specified translator type
     * 
     * @param translatorType the type of translator to create
     * @return the translation service instance
     * @throws IllegalArgumentException if the translator type is not supported
     * @throws IllegalStateException    if the required API key is not configured
     */
    public TranslationService createTranslationService(TranslatorType translatorType) {
        // Check cache first
        if (serviceCache.containsKey(translatorType)) {
            TranslationService cachedService = serviceCache.get(translatorType);
            if (cachedService.isAvailable()) {
                return cachedService;
            } else {
                // Remove unavailable service from cache
                serviceCache.remove(translatorType);
                logger.warn("Removed unavailable service from cache: {}", translatorType);
            }
        }

        // Create new service instance
        TranslationService service = createServiceInstance(translatorType);

        // Cache the service if it's available
        if (service.isAvailable()) {
            serviceCache.put(translatorType, service);
            logger.info("Created and cached translation service: {}", translatorType);
        } else {
            logger.warn("Created translation service is not available: {}", translatorType);
        }

        return service;
    }

    /**
     * Gets the default translation service based on configuration and availability
     * 
     * @return the default translation service
     * @throws IllegalStateException if no translation service is available
     */
    public TranslationService getDefaultTranslationService() {
        // Try DeepL first (default)
        if (configManager.getDeepLApiKeyFromPreferences().isPresent()) {
            try {
                TranslationService deeplService = createTranslationService(TranslatorType.DEEPL);
                if (deeplService.isAvailable()) {
                    logger.info("Using DeepL as default translation service");
                    return deeplService;
                }
            } catch (Exception e) {
                logger.warn("Failed to create DeepL service: {}", e.getMessage());
            }
        }

        // Fallback to Google Translate
        if (configManager.getGoogleApiKeyFromPreferences().isPresent()) {
            try {
                TranslationService googleService = createTranslationService(TranslatorType.GOOGLE);
                if (googleService.isAvailable()) {
                    logger.info("Using Google Translate as default translation service");
                    return googleService;
                }
            } catch (Exception e) {
                logger.warn("Failed to create Google Translate service: {}", e.getMessage());
            }
        }

        throw new IllegalStateException(
                "No translation service is available. Please check your API key configuration.");
    }

    /**
     * Gets an available translation service, preferring the specified type
     * 
     * @param preferredType the preferred translator type
     * @return an available translation service
     */
    public Optional<TranslationService> getAvailableTranslationService(TranslatorType preferredType) {
        try {
            TranslationService service = createTranslationService(preferredType);
            if (service.isAvailable()) {
                return Optional.of(service);
            }
        } catch (Exception e) {
            logger.debug("Preferred translation service not available: {}", preferredType, e);
        }

        // Try other available services
        for (TranslatorType type : TranslatorType.values()) {
            if (type != preferredType) {
                try {
                    TranslationService service = createTranslationService(type);
                    if (service.isAvailable()) {
                        logger.info("Using fallback translation service: {}", type);
                        return Optional.of(service);
                    }
                } catch (Exception e) {
                    logger.debug("Translation service not available: {}", type, e);
                }
            }
        }

        return Optional.empty();
    }

    /**
     * Checks which translation services are available
     * 
     * @return map of translator types to their availability status
     */
    public Map<TranslatorType, Boolean> checkServiceAvailability() {
        Map<TranslatorType, Boolean> availability = new HashMap<>();

        for (TranslatorType type : TranslatorType.values()) {
            try {
                TranslationService service = createServiceInstance(type);
                availability.put(type, service.isAvailable());
            } catch (Exception e) {
                availability.put(type, false);
                logger.debug("Service not available: {}", type, e);
            }
        }

        return availability;
    }

    /**
     * Clears the service cache, forcing recreation of services
     */
    public void clearCache() {
        serviceCache.clear();
        logger.info("Translation service cache cleared");
    }

    /**
     * Gets the number of cached services
     */
    public int getCacheSize() {
        return serviceCache.size();
    }

    /**
     * Creates a new service instance without caching
     */
    private TranslationService createServiceInstance(TranslatorType translatorType) {
        switch (translatorType) {
            case DEEPL:
                if (!configManager.getDeepLApiKeyFromPreferences().isPresent()) {
                    throw new IllegalStateException(
                            "DeepL API key not configured. Please set DEEPL_API_KEY environment variable or configure deepl.api.key in application.conf");
                }
                return new DeepLTranslationService(configManager);

            case GOOGLE:
                if (!configManager.getGoogleApiKeyFromPreferences().isPresent()) {
                    throw new IllegalStateException(
                            "Google API key not configured. Please set GOOGLE_API_KEY environment variable or configure google.api.key in application.conf");
                }
                return new GoogleTranslationService(configManager);

            default:
                throw new IllegalArgumentException("Unsupported translator type: " + translatorType);
        }
    }

    /**
     * Clears the service cache to force recreation with updated configuration
     * This should be called when API keys or other configuration changes
     */
    public void refreshServices() {
        logger.info("Refreshing translation services cache due to configuration changes");
        serviceCache.clear();
    }

    /**
     * Performs health checks on all available services
     */
    public void performHealthChecks() {
        logger.info("Performing health checks on translation services...");

        for (TranslatorType type : TranslatorType.values()) {
            try {
                TranslationService service = createServiceInstance(type);
                service.healthCheck();
                logger.info("✓ {} health check passed", type);
            } catch (Exception e) {
                logger.warn("✗ {} health check failed: {}", type, e.getMessage());
            }
        }
    }

    /**
     * Cleanup method for shutdown
     */
    public void cleanup() {
        serviceCache.clear();
        logger.info("Translation Service Factory cleanup completed");
    }
}
