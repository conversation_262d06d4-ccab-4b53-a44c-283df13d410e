package com.planb.exceltranslator.infrastructure.translation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.planb.exceltranslator.domain.model.Language;
import com.planb.exceltranslator.domain.model.TranslatorType;
import com.planb.exceltranslator.domain.port.TranslationException;
import com.planb.exceltranslator.domain.port.TranslationService;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * DeepL translation service implementation
 */
public class DeepLTranslationService implements TranslationService {

    private static final Logger logger = LoggerFactory.getLogger(DeepLTranslationService.class);
    private static final String DEEPL_API_URL = "https://api-free.deepl.com/v2/translate";
    private static final int MAX_BATCH_SIZE = 50;
    private static final int RATE_LIMIT = 5;

    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final String apiKey;

    public DeepLTranslationService(ConfigurationManager configManager) {
        this.apiKey = configManager.getDeepLApiKeyFromPreferences()
                .orElseThrow(() -> new IllegalStateException("DeepL API key not configured"));

        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();

        this.objectMapper = new ObjectMapper();

        logger.info("DeepL Translation Service initialized");
    }

    @Override
    public TranslatorType getTranslatorType() {
        return TranslatorType.DEEPL;
    }

    @Override
    public String translate(String text, Language sourceLanguage, Language targetLanguage)
            throws TranslationException {

        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        List<String> results = translateBatch(Arrays.asList(text), sourceLanguage, targetLanguage);
        return results.isEmpty() ? text : results.get(0);
    }

    @Override
    public List<String> translateBatch(List<String> texts, Language sourceLanguage, Language targetLanguage)
            throws TranslationException {

        if (texts == null || texts.isEmpty()) {
            return new ArrayList<>();
        }

        validateLanguagePair(sourceLanguage, targetLanguage);

        try {
            FormBody.Builder formBuilder = new FormBody.Builder()
                    .add("auth_key", apiKey)
                    .add("target_lang", mapLanguageToDeepL(targetLanguage));

            // Add source language if not auto-detect
            if (sourceLanguage != Language.AUTO_DETECT) {
                formBuilder.add("source_lang", mapLanguageToDeepL(sourceLanguage));
            }

            // Add all texts
            for (String text : texts) {
                if (text != null && !text.trim().isEmpty()) {
                    formBuilder.add("text", text);
                }
            }

            RequestBody formBody = formBuilder.build();

            Request request = new Request.Builder()
                    .url(DEEPL_API_URL)
                    .post(formBody)
                    .addHeader("User-Agent", "ExcelTranslator/1.0")
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    handleErrorResponse(response);
                }

                String responseBody = response.body().string();
                return parseTranslationResponse(responseBody);
            }

        } catch (IOException e) {
            logger.error("Network error during DeepL translation", e);
            throw new TranslationException("Network error during translation",
                    TranslationException.ErrorType.NETWORK_ERROR,
                    "Unable to connect to DeepL service. Please check your internet connection.", e);
        }
    }

    @Override
    public CompletableFuture<List<String>> translateBatchAsync(List<String> texts,
            Language sourceLanguage,
            Language targetLanguage) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return translateBatch(texts, sourceLanguage, targetLanguage);
            } catch (TranslationException e) {
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public Language detectLanguage(String text) throws TranslationException {
        if (text == null || text.trim().isEmpty()) {
            return Language.AUTO_DETECT;
        }

        try {
            translateBatch(Arrays.asList(text), Language.AUTO_DETECT, Language.ENGLISH);
            return Language.AUTO_DETECT;
        } catch (TranslationException e) {
            logger.warn("Failed to detect language for text: {}", text.substring(0, Math.min(50, text.length())));
            return Language.AUTO_DETECT;
        }
    }

    @Override
    public boolean isAvailable() {
        try {
            healthCheck();
            return true;
        } catch (TranslationException e) {
            return false;
        }
    }

    @Override
    public int getMaxBatchSize() {
        return MAX_BATCH_SIZE;
    }

    @Override
    public int getRateLimit() {
        return RATE_LIMIT;
    }

    @Override
    public List<Language> getSupportedSourceLanguages() {
        return Arrays.asList(Language.AUTO_DETECT, Language.ENGLISH, Language.JAPANESE, Language.VIETNAMESE);
    }

    @Override
    public List<Language> getSupportedTargetLanguages() {
        return Arrays.asList(Language.ENGLISH, Language.JAPANESE, Language.VIETNAMESE);
    }

    @Override
    public boolean isLanguagePairSupported(Language sourceLanguage, Language targetLanguage) {
        return getSupportedSourceLanguages().contains(sourceLanguage) &&
                getSupportedTargetLanguages().contains(targetLanguage);
    }

    @Override
    public void healthCheck() throws TranslationException {
        try {
            // Test with a simple translation
            translate("Hello", Language.ENGLISH, Language.JAPANESE);
            logger.debug("DeepL health check passed");
        } catch (Exception e) {
            logger.error("DeepL health check failed", e);
            throw new TranslationException("DeepL service health check failed",
                    TranslationException.ErrorType.SERVICE_UNAVAILABLE,
                    "DeepL translation service is not available", e);
        }
    }

    /**
     * Maps internal Language enum to DeepL language codes
     */
    private String mapLanguageToDeepL(Language language) {
        switch (language) {
            case ENGLISH:
                return "EN";
            case JAPANESE:
                return "JA";
            case VIETNAMESE:
                return "VI";
            default:
                throw new IllegalArgumentException("Unsupported language for DeepL: " + language);
        }
    }

    /**
     * Validates that the language pair is supported
     */
    private void validateLanguagePair(Language sourceLanguage, Language targetLanguage)
            throws TranslationException {
        if (!isLanguagePairSupported(sourceLanguage, targetLanguage)) {
            throw new TranslationException(
                    String.format("Language pair %s -> %s not supported by DeepL",
                            sourceLanguage, targetLanguage),
                    TranslationException.ErrorType.UNSUPPORTED_LANGUAGE,
                    String.format("The language combination %s to %s is not supported by DeepL Translator",
                            sourceLanguage.getEnglishName(), targetLanguage.getEnglishName()));
        }
    }

    /**
     * Handles error responses from DeepL API
     */
    private void handleErrorResponse(Response response) throws TranslationException {
        int code = response.code();
        String message = "DeepL API error: " + code;

        try {
            String body = response.body().string();
            JsonNode errorNode = objectMapper.readTree(body);
            if (errorNode.has("message")) {
                message = errorNode.get("message").asText();
            }
        } catch (Exception e) {
            logger.debug("Failed to parse error response", e);
        }

        TranslationException.ErrorType errorType;
        String userMessage;

        switch (code) {
            case 400:
                errorType = TranslationException.ErrorType.INVALID_REQUEST;
                userMessage = "Invalid translation request. Please check your input.";
                break;
            case 403:
                errorType = TranslationException.ErrorType.API_KEY_INVALID;
                userMessage = "DeepL API key is invalid. Please check your configuration.";
                break;
            case 429:
                errorType = TranslationException.ErrorType.RATE_LIMIT_EXCEEDED;
                userMessage = "DeepL rate limit exceeded. Please wait and try again.";
                break;
            case 456:
                errorType = TranslationException.ErrorType.QUOTA_EXCEEDED;
                userMessage = "DeepL translation quota exceeded. Please check your account.";
                break;
            default:
                errorType = TranslationException.ErrorType.SERVICE_UNAVAILABLE;
                userMessage = "DeepL service is temporarily unavailable. Please try again later.";
                break;
        }

        throw new TranslationException(message, errorType, userMessage);
    }

    /**
     * Parses the translation response from DeepL
     */
    private List<String> parseTranslationResponse(String responseBody) throws TranslationException {
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            JsonNode translations = root.get("translations");

            List<String> results = new ArrayList<>();
            if (translations.isArray()) {
                for (JsonNode translation : translations) {
                    String translatedText = translation.get("text").asText();
                    results.add(translatedText);
                }
            }

            return results;

        } catch (Exception e) {
            logger.error("Failed to parse DeepL response: {}", responseBody, e);
            throw new TranslationException("Failed to parse translation response",
                    TranslationException.ErrorType.GENERAL,
                    "Received invalid response from DeepL service", e);
        }
    }
}
