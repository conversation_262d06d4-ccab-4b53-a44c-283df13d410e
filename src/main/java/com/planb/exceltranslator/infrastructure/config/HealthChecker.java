package com.planb.exceltranslator.infrastructure.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Performs health checks for the application including configuration,
 * API credentials, and internet connectivity
 */
public class HealthChecker {
    
    private static final Logger logger = LoggerFactory.getLogger(HealthChecker.class);
    private static final String CONNECTIVITY_TEST_URL = "https://www.google.com";
    private static final int CONNECTIVITY_TIMEOUT_MS = 5000;
    
    private final ConfigurationManager configManager;
    
    public HealthChecker(ConfigurationManager configManager) {
        this.configManager = configManager;
    }
    
    /**
     * Performs a comprehensive health check
     */
    public HealthCheckResult performHealthCheck() {
        HealthCheckResult.Builder resultBuilder = HealthCheckResult.builder();
        
        // Check configuration
        try {
            checkConfiguration();
            resultBuilder.configurationHealthy(true);
        } catch (Exception e) {
            resultBuilder.configurationHealthy(false)
                    .addError("Configuration check failed: " + e.getMessage());
        }
        
        // Check API credentials
        try {
            checkApiCredentials();
            resultBuilder.apiCredentialsHealthy(true);
        } catch (Exception e) {
            resultBuilder.apiCredentialsHealthy(false)
                    .addError("API credentials check failed: " + e.getMessage());
        }
        
        // Check internet connectivity
        try {
            checkInternetConnectivity();
            resultBuilder.connectivityHealthy(true);
        } catch (Exception e) {
            resultBuilder.connectivityHealthy(false)
                    .addError("Connectivity check failed: " + e.getMessage());
        }
        
        return resultBuilder.build();
    }
    
    /**
     * Checks if configuration is valid and accessible
     */
    public void checkConfiguration() throws Exception {
        logger.debug("Checking configuration...");
        
        try {
            configManager.validateConfiguration();
            logger.debug("Configuration check passed");
        } catch (ConfigurationManager.ConfigurationException e) {
            logger.error("Configuration check failed: {}", e.getMessage());
            throw new HealthCheckException("Configuration validation failed", e);
        }
    }
    
    /**
     * Checks if API credentials are available
     */
    public void checkApiCredentials() throws Exception {
        logger.debug("Checking API credentials...");
        
        boolean hasDeepL = configManager.getDeepLApiKey().isPresent();
        boolean hasGoogle = configManager.getGoogleApiKey().isPresent();
        
        if (!hasDeepL && !hasGoogle) {
            throw new HealthCheckException("No API credentials found. Please configure at least one API key.");
        }
        
        if (hasDeepL) {
            String deeplKey = configManager.getDeepLApiKey().get();
            if (deeplKey.length() < 10) {
                throw new HealthCheckException("DeepL API key appears to be invalid (too short)");
            }
            logger.debug("DeepL API key found and appears valid");
        }
        
        if (hasGoogle) {
            String googleKey = configManager.getGoogleApiKey().get();
            if (googleKey.length() < 10) {
                throw new HealthCheckException("Google API key appears to be invalid (too short)");
            }
            logger.debug("Google API key found and appears valid");
        }
        
        logger.debug("API credentials check passed");
    }
    
    /**
     * Checks internet connectivity
     */
    public void checkInternetConnectivity() throws Exception {
        logger.debug("Checking internet connectivity...");
        
        try {
            CompletableFuture<Boolean> connectivityFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    URL url = new URL(CONNECTIVITY_TEST_URL);
                    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("HEAD");
                    connection.setConnectTimeout(CONNECTIVITY_TIMEOUT_MS);
                    connection.setReadTimeout(CONNECTIVITY_TIMEOUT_MS);
                    connection.connect();
                    
                    int responseCode = connection.getResponseCode();
                    connection.disconnect();
                    
                    return responseCode >= 200 && responseCode < 400;
                } catch (IOException e) {
                    logger.debug("Connectivity test failed: {}", e.getMessage());
                    return false;
                }
            });
            
            Boolean isConnected = connectivityFuture.get(10, TimeUnit.SECONDS);
            
            if (!isConnected) {
                throw new HealthCheckException("No internet connectivity detected");
            }
            
            logger.debug("Internet connectivity check passed");
            
        } catch (Exception e) {
            if (e instanceof HealthCheckException) {
                throw e;
            }
            throw new HealthCheckException("Failed to check internet connectivity", e);
        }
    }
    
    /**
     * Performs a quick health check (non-blocking)
     */
    public CompletableFuture<HealthCheckResult> performQuickHealthCheck() {
        return CompletableFuture.supplyAsync(this::performHealthCheck);
    }
    
    /**
     * Exception thrown when health checks fail
     */
    public static class HealthCheckException extends Exception {
        public HealthCheckException(String message) {
            super(message);
        }
        
        public HealthCheckException(String message, Throwable cause) {
            super(message, cause);
        }
    }
    
    /**
     * Result of health check operations
     */
    public static class HealthCheckResult {
        private final boolean configurationHealthy;
        private final boolean apiCredentialsHealthy;
        private final boolean connectivityHealthy;
        private final java.util.List<String> errors;
        private final java.util.List<String> warnings;
        
        private HealthCheckResult(Builder builder) {
            this.configurationHealthy = builder.configurationHealthy;
            this.apiCredentialsHealthy = builder.apiCredentialsHealthy;
            this.connectivityHealthy = builder.connectivityHealthy;
            this.errors = new java.util.ArrayList<>(builder.errors);
            this.warnings = new java.util.ArrayList<>(builder.warnings);
        }
        
        public boolean isConfigurationHealthy() { return configurationHealthy; }
        public boolean isApiCredentialsHealthy() { return apiCredentialsHealthy; }
        public boolean isConnectivityHealthy() { return connectivityHealthy; }
        public java.util.List<String> getErrors() { return new java.util.ArrayList<>(errors); }
        public java.util.List<String> getWarnings() { return new java.util.ArrayList<>(warnings); }
        
        public boolean isOverallHealthy() {
            return configurationHealthy && apiCredentialsHealthy && connectivityHealthy;
        }
        
        public static Builder builder() {
            return new Builder();
        }
        
        public static class Builder {
            private boolean configurationHealthy = false;
            private boolean apiCredentialsHealthy = false;
            private boolean connectivityHealthy = false;
            private final java.util.List<String> errors = new java.util.ArrayList<>();
            private final java.util.List<String> warnings = new java.util.ArrayList<>();
            
            public Builder configurationHealthy(boolean healthy) {
                this.configurationHealthy = healthy;
                return this;
            }
            
            public Builder apiCredentialsHealthy(boolean healthy) {
                this.apiCredentialsHealthy = healthy;
                return this;
            }
            
            public Builder connectivityHealthy(boolean healthy) {
                this.connectivityHealthy = healthy;
                return this;
            }
            
            public Builder addError(String error) {
                if (error != null) this.errors.add(error);
                return this;
            }
            
            public Builder addWarning(String warning) {
                if (warning != null) this.warnings.add(warning);
                return this;
            }
            
            public HealthCheckResult build() {
                return new HealthCheckResult(this);
            }
        }
    }
}
