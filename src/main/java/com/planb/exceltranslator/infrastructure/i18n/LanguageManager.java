package com.planb.exceltranslator.infrastructure.i18n;

import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Language Manager for handling internationalization and dynamic language switching
 * Provides reactive string properties that update automatically when language changes
 */
public class LanguageManager {

    private static final Logger logger = LoggerFactory.getLogger(LanguageManager.class);
    private static LanguageManager instance;

    private Locale currentLocale;
    private ResourceBundle currentBundle;
    private final Map<String, StringProperty> stringProperties = new ConcurrentHashMap<>();
    private final List<LanguageChangeListener> listeners = new ArrayList<>();

    private static final Map<String, Locale> SUPPORTED_LANGUAGES = Map.of(
            "en", Locale.ENGLISH,
            "ja", Locale.JAPANESE,
            "vi", new Locale("vi", "VN"));

    private LanguageManager() {
        setLanguage("en");
    }

    public static synchronized LanguageManager getInstance() {
        if (instance == null) {
            instance = new LanguageManager();
        }
        return instance;
    }

    /**
     * Interface for language change notifications
     */
    public interface LanguageChangeListener {
        void onLanguageChanged(Locale newLocale);
    }

    /**
     * Sets the current language and updates all string properties
     * 
     * @param languageCode ISO language code (en, ja, vi)
     */
    public void setLanguage(String languageCode) {
        try {
            Locale newLocale = SUPPORTED_LANGUAGES.getOrDefault(languageCode, Locale.ENGLISH);

            if (newLocale.equals(currentLocale)) {
                return;
            }

            logger.info("Changing language to: {} ({})", languageCode, newLocale.getDisplayName());

            this.currentLocale = newLocale;
            this.currentBundle = ResourceBundle.getBundle("i18n.messages", currentLocale);

            updateAllStringProperties();

            notifyLanguageChangeListeners();

            logger.info("Language changed successfully to: {}", currentLocale.getDisplayName());

        } catch (Exception e) {
            logger.error("Failed to change language to: {}", languageCode, e);
            if (!languageCode.equals("en")) {
                setLanguage("en");
            }
        }
    }

    /**
     * Gets a localized string for the given key
     * 
     * @param key the message key
     * @return localized string or the key if not found
     */
    public String getString(String key) {
        try {
            return currentBundle.getString(key);
        } catch (MissingResourceException e) {
            logger.warn("Missing translation for key: {}", key);
            return key; // Return the key itself if translation is missing
        }
    }

    /**
     * Gets a reactive StringProperty that updates automatically when language
     * changes
     * 
     * @param key the message key
     * @return StringProperty that updates with language changes
     */
    public StringProperty getStringProperty(String key) {
        return stringProperties.computeIfAbsent(key, k -> {
            StringProperty property = new SimpleStringProperty(getString(k));
            logger.debug("Created reactive string property for key: {}", k);
            return property;
        });
    }

    /**
     * Gets the current locale
     * 
     * @return current locale
     */
    public Locale getCurrentLocale() {
        return currentLocale;
    }

    /**
     * Gets the current language code
     * 
     * @return current language code
     */
    public String getCurrentLanguageCode() {
        return currentLocale.getLanguage();
    }

    /**
     * Gets all supported languages
     * 
     * @return map of language codes to locales
     */
    public Map<String, Locale> getSupportedLanguages() {
        return new HashMap<>(SUPPORTED_LANGUAGES);
    }

    /**
     * Adds a language change listener
     * 
     * @param listener the listener to add
     */
    public void addLanguageChangeListener(LanguageChangeListener listener) {
        listeners.add(listener);
    }

    /**
     * Removes a language change listener
     * 
     * @param listener the listener to remove
     */
    public void removeLanguageChangeListener(LanguageChangeListener listener) {
        listeners.remove(listener);
    }

    /**
     * Updates all existing string properties with current language
     */
    private void updateAllStringProperties() {
        stringProperties.forEach((key, property) -> {
            String newValue = getString(key);
            property.set(newValue);
            logger.debug("Updated property '{}' to: {}", key, newValue);
        });
    }

    private void notifyLanguageChangeListeners() {
        for (LanguageChangeListener listener : listeners) {
            try {
                listener.onLanguageChanged(currentLocale);
            } catch (Exception e) {
                logger.error("Error notifying language change listener", e);
            }
        }
    }

    public void clearCache() {
        stringProperties.clear();
        logger.debug("Cleared string property cache");
    }
}
