package com.planb.exceltranslator.infrastructure.excel;

import com.planb.exceltranslator.domain.model.ExcelSheet;
import com.planb.exceltranslator.domain.model.TranslatableCell;
import com.planb.exceltranslator.domain.port.ExcelProcessor;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Apache POI implementation of Excel processor
 * Handles both .xlsx and .xls file formats
 */
public class ApachePOIExcelProcessor implements ExcelProcessor {

    private static final Logger logger = LoggerFactory.getLogger(ApachePOIExcelProcessor.class);
    private static final String[] SUPPORTED_EXTENSIONS = { ".xlsx", ".xls" };

    @Override
    public List<ExcelSheet> readExcelFile(File excelFile) throws IOException {
        if (!isSupportedExcelFile(excelFile)) {
            throw new UnsupportedOperationException("Unsupported file format: " + getFileExtension(excelFile));
        }

        logger.info("Reading Excel file: {}", excelFile.getName());

        List<ExcelSheet> sheets = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(excelFile);
                Workbook workbook = createWorkbook(fis, getFileExtension(excelFile))) {

            int numberOfSheets = workbook.getNumberOfSheets();
            logger.debug("Found {} sheets in Excel file", numberOfSheets);

            for (int i = 0; i < numberOfSheets; i++) {
                Sheet sheet = workbook.getSheetAt(i);
                ExcelSheet excelSheet = processSheet(sheet, i);

                if (excelSheet.hasTranslatableCells()) {
                    sheets.add(excelSheet);
                    logger.debug("Processed sheet '{}' with {} translatable cells",
                            excelSheet.getName(), excelSheet.getTranslatableCellCount());
                } else {
                    logger.debug("Skipped sheet '{}' - no translatable cells found", sheet.getSheetName());
                }
            }

        } catch (Exception e) {
            logger.error("Failed to read Excel file: {}", excelFile.getName(), e);
            throw new IOException("Failed to read Excel file: " + e.getMessage(), e);
        }

        logger.info("Successfully read {} sheets with translatable content from {}",
                sheets.size(), excelFile.getName());

        return sheets;
    }

    @Override
    public void writeTranslatedExcel(File originalFile, List<ExcelSheet> translatedSheets, File outputFile)
            throws IOException {

        logger.info("Writing translated Excel file: {}", outputFile.getName());

        try (FileInputStream fis = new FileInputStream(originalFile);
                Workbook workbook = createWorkbook(fis, getFileExtension(originalFile))) {

            // Update the workbook with translated content
            for (ExcelSheet translatedSheet : translatedSheets) {
                Sheet sheet = workbook.getSheetAt(translatedSheet.getIndex());
                updateSheetWithTranslations(sheet, translatedSheet);
            }

            // Write the updated workbook to the output file
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }

        } catch (Exception e) {
            logger.error("Failed to write translated Excel file: {}", outputFile.getName(), e);
            throw new IOException("Failed to write translated Excel file: " + e.getMessage(), e);
        }

        logger.info("Successfully wrote translated Excel file: {}", outputFile.getName());
    }

    @Override
    public boolean isSupportedExcelFile(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return false;
        }

        String extension = getFileExtension(file).toLowerCase();
        for (String supportedExt : SUPPORTED_EXTENSIONS) {
            if (supportedExt.equals(extension)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public String getFileExtension(File file) {
        String fileName = file.getName();
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";
    }

    @Override
    public int estimateProcessingTime(File excelFile) throws IOException {
        ExcelFileInfo fileInfo = getFileInfo(excelFile);

        int translatableCells = fileInfo.getTranslatableCells();
        int estimatedRequests = (int) Math.ceil((double) translatableCells / 50);
        int estimatedSeconds = (int) Math.ceil((double) estimatedRequests / 5);

        return Math.max(estimatedSeconds, 5);
    }

    @Override
    public ExcelFileInfo getFileInfo(File excelFile) throws IOException {
        if (!isSupportedExcelFile(excelFile)) {
            throw new UnsupportedOperationException("Unsupported file format: " + getFileExtension(excelFile));
        }

        int sheetCount = 0;
        int totalCells = 0;
        int translatableCells = 0;

        try (FileInputStream fis = new FileInputStream(excelFile);
                Workbook workbook = createWorkbook(fis, getFileExtension(excelFile))) {

            sheetCount = workbook.getNumberOfSheets();

            for (int i = 0; i < sheetCount; i++) {
                Sheet sheet = workbook.getSheetAt(i);

                for (Row row : sheet) {
                    for (Cell cell : row) {
                        totalCells++;

                        String cellValue = getCellValueAsString(cell);
                        if (TranslatableCell.isTranslatable(cellValue)) {
                            translatableCells++;
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.error("Failed to analyze Excel file: {}", excelFile.getName(), e);
            throw new IOException("Failed to analyze Excel file: " + e.getMessage(), e);
        }

        return new ExcelFileInfo(
                excelFile.getName(),
                excelFile.length(),
                sheetCount,
                totalCells,
                translatableCells,
                getFileExtension(excelFile));
    }

    @Override
    public File createBackup(File originalFile) throws IOException {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String backupName = getFileNameWithoutExtension(originalFile) + "_backup_" + timestamp
                + getFileExtension(originalFile);
        File backupFile = new File(originalFile.getParent(), backupName);

        Files.copy(originalFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);

        logger.info("Created backup file: {}", backupFile.getName());
        return backupFile;
    }

    /**
     * Creates a workbook instance based on file extension
     */
    private Workbook createWorkbook(InputStream inputStream, String extension) throws IOException {
        switch (extension.toLowerCase()) {
            case ".xlsx":
                return new XSSFWorkbook(inputStream);
            case ".xls":
                return new HSSFWorkbook(inputStream);
            default:
                throw new UnsupportedOperationException("Unsupported file format: " + extension);
        }
    }

    /**
     * Processes a single sheet and extracts translatable cells
     */
    private ExcelSheet processSheet(Sheet sheet, int index) {
        ExcelSheet excelSheet = new ExcelSheet(sheet.getSheetName(), index);

        for (Row row : sheet) {
            for (Cell cell : row) {
                String cellValue = getCellValueAsString(cell);

                if (TranslatableCell.isTranslatable(cellValue)) {
                    TranslatableCell translatableCell = new TranslatableCell(
                            row.getRowNum(),
                            cell.getColumnIndex(),
                            cellValue);
                    excelSheet.addTranslatableCell(translatableCell);
                }
            }
        }

        return excelSheet;
    }

    /**
     * Updates a sheet with translated content
     */
    private void updateSheetWithTranslations(Sheet sheet, ExcelSheet translatedSheet) {
        // Update sheet name if it has been translated
        if (translatedSheet.isNameTranslated()) {
            try {
                Workbook workbook = sheet.getWorkbook();
                workbook.setSheetName(translatedSheet.getIndex(), translatedSheet.getTranslatedName());
                logger.debug("Updated sheet name from '{}' to '{}'",
                        translatedSheet.getName(), translatedSheet.getTranslatedName());
            } catch (Exception e) {
                logger.warn("Failed to update sheet name for sheet '{}': {}",
                        translatedSheet.getName(), e.getMessage());
            }
        }

        // Update cell content
        for (TranslatableCell translatableCell : translatedSheet.getTranslatableCells()) {
            if (translatableCell.isTranslated()) {
                Row row = sheet.getRow(translatableCell.getRow());
                if (row != null) {
                    Cell cell = row.getCell(translatableCell.getColumn());
                    if (cell != null) {
                        cell.setCellValue(translatableCell.getTranslatedText());
                    }
                }
            }
        }
    }

    /**
     * Extracts cell value as string, handling different cell types
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // Check if it's a whole number
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    return cell.getCellFormula();
                }
            case BLANK:
            case _NONE:
            default:
                return "";
        }
    }

    /**
     * Gets file name without extension
     */
    private String getFileNameWithoutExtension(File file) {
        String fileName = file.getName();
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }
}
