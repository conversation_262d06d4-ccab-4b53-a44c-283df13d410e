package com.planb.exceltranslator.application;

import com.planb.exceltranslator.domain.model.Language;
import com.planb.exceltranslator.domain.model.TranslatorType;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Consumer;

public class UIService {
    
    private static final Logger logger = LoggerFactory.getLogger(UIService.class);
    
    private final ConfigurationManager configManager;
    private Consumer<String> logCallback;
    private Consumer<UIStateUpdate> uiStateCallback;
    
    public UIService(ConfigurationManager configManager) {
        this.configManager = configManager;
        logger.info("UI Service initialized");
    }
    
    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
    
    public void setUIStateCallback(Consumer<UIStateUpdate> uiStateCallback) {
        this.uiStateCallback = uiStateCallback;
    }
    
    public int calculateOptimalBatchSize(int totalCells) {
        final int MAX_BATCH_SIZE = configManager.getMaxBatchSize();
        final int MIN_BATCH_SIZE = 1;
        final int DEFAULT_BATCH_SIZE = configManager.getDefaultBatchSize();

        if (totalCells <= 50) {
            return Math.max(MIN_BATCH_SIZE, Math.min(10, totalCells));
        } else if (totalCells <= 200) {
            return Math.min(25, MAX_BATCH_SIZE);
        } else if (totalCells <= 1000) {
            return Math.min(DEFAULT_BATCH_SIZE, MAX_BATCH_SIZE);
        } else if (totalCells <= 5000) {
            return Math.min(75, MAX_BATCH_SIZE);
        } else {
            return MAX_BATCH_SIZE;
        }
    }
    
    public Language getAlternativeTargetLanguage(Language sourceLanguage) {
        switch (sourceLanguage) {
            case ENGLISH:
                return Language.JAPANESE;
            case JAPANESE:
                return Language.ENGLISH;
            case VIETNAMESE:
                return Language.ENGLISH;
            default:
                return Language.ENGLISH;
        }
    }
    
    public ValidationResult validateTranslationSettings(ValidationRequest request) {
        ValidationResult.Builder result = ValidationResult.builder();
        
        if (request.getSelectedFile() == null) {
            result.addError("error.title.no_file", "error.message.no_file_selected");
        }
        
        if (request.getDetectedSheets() == null || request.getDetectedSheets().isEmpty()) {
            result.addError("error.title.no_sheets", "error.message.no_sheets_detected");
        }
        
        if (request.getTargetLanguage() == null) {
            result.addError("error.title.invalid_selection", "error.message.select_target_language");
        }
        
        if (request.getTranslator() == null) {
            result.addError("error.title.invalid_selection", "error.message.select_translator");
        }
        
        if (request.getSelectedSheets() == null || request.getSelectedSheets().isEmpty()) {
            result.addError("error.title.no_sheets", "error.message.select_sheets");
        }
        
        return result.build();
    }
    public void updateUIState(UIStateUpdate update) {
        if (uiStateCallback != null) {
            uiStateCallback.accept(update);
        }
    }
    
    /**
     * Logs a message
     */
    public void logMessage(String message) {
        if (logCallback != null) {
            logCallback.accept(message);
        }
        logger.info("UI Log: {}", message);
    }
    
    public UIStateUpdate createResetState() {
        return UIStateUpdate.builder()
                .sourceLanguage(Language.AUTO_DETECT)
                .targetLanguage(Language.ENGLISH)
                .translator(TranslatorType.DEEPL)
                .batchSize(configManager.getDefaultBatchSize())
                .resetFileSelection(true)
                .build();
    }
    
    public void cleanup() {
        logger.info("UI Service cleanup completed");
    }
    
    public static class ValidationRequest {
        private final java.io.File selectedFile;
        private final java.util.List<com.planb.exceltranslator.domain.model.ExcelSheet> detectedSheets;
        private final Language targetLanguage;
        private final TranslatorType translator;
        private final java.util.List<com.planb.exceltranslator.domain.model.ExcelSheet> selectedSheets;
        
        public ValidationRequest(java.io.File selectedFile, 
                               java.util.List<com.planb.exceltranslator.domain.model.ExcelSheet> detectedSheets,
                               Language targetLanguage, 
                               TranslatorType translator,
                               java.util.List<com.planb.exceltranslator.domain.model.ExcelSheet> selectedSheets) {
            this.selectedFile = selectedFile;
            this.detectedSheets = detectedSheets;
            this.targetLanguage = targetLanguage;
            this.translator = translator;
            this.selectedSheets = selectedSheets;
        }
        
        public java.io.File getSelectedFile() { return selectedFile; }
        public java.util.List<com.planb.exceltranslator.domain.model.ExcelSheet> getDetectedSheets() { return detectedSheets; }
        public Language getTargetLanguage() { return targetLanguage; }
        public TranslatorType getTranslator() { return translator; }
        public java.util.List<com.planb.exceltranslator.domain.model.ExcelSheet> getSelectedSheets() { return selectedSheets; }
    }
    
    public static class ValidationResult {
        private final java.util.List<String> errors;
        private final boolean valid;
        
        private ValidationResult(java.util.List<String> errors) {
            this.errors = java.util.Collections.unmodifiableList(errors);
            this.valid = errors.isEmpty();
        }
        
        public boolean isValid() { return valid; }
        public java.util.List<String> getErrors() { return errors; }
        
        public static Builder builder() {
            return new Builder();
        }
        
        public static class Builder {
            private final java.util.List<String> errors = new java.util.ArrayList<>();
            
            public Builder addError(String titleKey, String messageKey) {
                errors.add(titleKey + ": " + messageKey);
                return this;
            }
            
            public ValidationResult build() {
                return new ValidationResult(errors);
            }
        }
    }
    
    public static class UIStateUpdate {
        private final Language sourceLanguage;
        private final Language targetLanguage;
        private final TranslatorType translator;
        private final Integer batchSize;
        private final boolean resetFileSelection;
        
        private UIStateUpdate(Builder builder) {
            this.sourceLanguage = builder.sourceLanguage;
            this.targetLanguage = builder.targetLanguage;
            this.translator = builder.translator;
            this.batchSize = builder.batchSize;
            this.resetFileSelection = builder.resetFileSelection;
        }
        
        // Getters
        public Language getSourceLanguage() { return sourceLanguage; }
        public Language getTargetLanguage() { return targetLanguage; }
        public TranslatorType getTranslator() { return translator; }
        public Integer getBatchSize() { return batchSize; }
        public boolean isResetFileSelection() { return resetFileSelection; }
        
        public static Builder builder() {
            return new Builder();
        }
        
        public static class Builder {
            private Language sourceLanguage;
            private Language targetLanguage;
            private TranslatorType translator;
            private Integer batchSize;
            private boolean resetFileSelection;
            
            public Builder sourceLanguage(Language sourceLanguage) {
                this.sourceLanguage = sourceLanguage;
                return this;
            }
            
            public Builder targetLanguage(Language targetLanguage) {
                this.targetLanguage = targetLanguage;
                return this;
            }
            
            public Builder translator(TranslatorType translator) {
                this.translator = translator;
                return this;
            }
            
            public Builder batchSize(Integer batchSize) {
                this.batchSize = batchSize;
                return this;
            }
            
            public Builder resetFileSelection(boolean resetFileSelection) {
                this.resetFileSelection = resetFileSelection;
                return this;
            }
            
            public UIStateUpdate build() {
                return new UIStateUpdate(this);
            }
        }
    }
}
