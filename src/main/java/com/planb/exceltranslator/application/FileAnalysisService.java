package com.planb.exceltranslator.application;

import com.planb.exceltranslator.domain.model.ExcelSheet;
import com.planb.exceltranslator.domain.port.ExcelProcessor;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

public class FileAnalysisService {
    
    private static final Logger logger = LoggerFactory.getLogger(FileAnalysisService.class);
    
    private final ConfigurationManager configManager;
    private final TranslationApplicationService translationService;
    private Consumer<String> logCallback;
    private Consumer<AnalysisProgress> progressCallback;
    
    public FileAnalysisService(ConfigurationManager configManager, 
                              TranslationApplicationService translationService) {
        this.configManager = configManager;
        this.translationService = translationService;
        logger.info("File Analysis Service initialized");
    }
    
    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
    
    public void setProgressCallback(Consumer<AnalysisProgress> progressCallback) {
        this.progressCallback = progressCallback;
    }
    
    public FileValidationResult validateFile(File file) {
        if (!file.getName().toLowerCase().endsWith(".xlsx") && 
            !file.getName().toLowerCase().endsWith(".xls")) {
            return FileValidationResult.error("Invalid file type - only .xlsx and .xls files are supported");
        }
        
        long fileSizeBytes = file.length();
        long maxSizeBytes = (long) configManager.getMaxFileSizeMB() * 1024 * 1024;
        
        if (fileSizeBytes > maxSizeBytes) {
            return FileValidationResult.error(
                String.format("File size (%.1f MB) exceeds maximum allowed size (%d MB)", 
                    (double) fileSizeBytes / (1024 * 1024), configManager.getMaxFileSizeMB()));
        }
        
        return FileValidationResult.success();
    }
    
    public CompletableFuture<AnalysisResult> analyzeFileAsync(File file) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logMessage("========== FILE ANALYSIS STARTED ==========");
                logMessage("Analyzing file structure...");
                updateProgress(10, "Reading file information...");
                
                ExcelProcessor.ExcelFileInfo fileInfo = translationService.getFileInfo(file);
                logMessage("File info: " + fileInfo.getSheetCount() + " sheets, " + 
                          fileInfo.getTranslatableCells() + " translatable cells");
                
                updateProgress(50, "Analyzing sheets...");
                
                List<ExcelSheet> sheets = translationService.analyzeExcelFile(file);
                
                updateProgress(100, "Analysis completed");
                logMessage("File analyzed: " + sheets.size() + " sheets with translatable content found");
                logMessage("========== FILE ANALYSIS COMPLETED ==========");
                
                return AnalysisResult.success(file, fileInfo, sheets);
                
            } catch (Exception e) {
                logger.error("Failed to analyze Excel file", e);
                logMessage("ERROR: Failed to analyze file - " + e.getMessage());
                return AnalysisResult.error(e.getMessage());
            }
        });
    }
    
    public CompletableFuture<ExcelProcessor.ExcelFileInfo> getFileInfoAsync(File file) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return translationService.getFileInfo(file);
            } catch (IOException e) {
                logger.error("Failed to get file info for file: {}", file.getName(), e);
                throw new RuntimeException("Failed to read file information: " + e.getMessage(), e);
            }
        });
    }
    
    private void logMessage(String message) {
        if (logCallback != null) {
            logCallback.accept(message);
        }
        logger.info("File Analysis: {}", message);
    }
    
    private void updateProgress(int percent, String message) {
        if (progressCallback != null) {
            progressCallback.accept(new AnalysisProgress(percent, message));
        }
    }
    
    public void cleanup() {
        logger.info("File Analysis Service cleanup completed");
    }
    
    // Inner classes for data transfer
    public static class FileValidationResult {
        private final boolean valid;
        private final String errorMessage;
        
        private FileValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }
        
        public boolean isValid() { return valid; }
        public String getErrorMessage() { return errorMessage; }
        
        public static FileValidationResult success() {
            return new FileValidationResult(true, null);
        }
        
        public static FileValidationResult error(String message) {
            return new FileValidationResult(false, message);
        }
    }
    
    public static class AnalysisResult {
        private final boolean success;
        private final File file;
        private final ExcelProcessor.ExcelFileInfo fileInfo;
        private final List<ExcelSheet> sheets;
        private final String errorMessage;
        
        private AnalysisResult(boolean success, File file, ExcelProcessor.ExcelFileInfo fileInfo, 
                              List<ExcelSheet> sheets, String errorMessage) {
            this.success = success;
            this.file = file;
            this.fileInfo = fileInfo;
            this.sheets = sheets;
            this.errorMessage = errorMessage;
        }
        
        public boolean isSuccess() { return success; }
        public File getFile() { return file; }
        public ExcelProcessor.ExcelFileInfo getFileInfo() { return fileInfo; }
        public List<ExcelSheet> getSheets() { return sheets; }
        public String getErrorMessage() { return errorMessage; }
        
        public static AnalysisResult success(File file, ExcelProcessor.ExcelFileInfo fileInfo, List<ExcelSheet> sheets) {
            return new AnalysisResult(true, file, fileInfo, sheets, null);
        }
        
        public static AnalysisResult error(String errorMessage) {
            return new AnalysisResult(false, null, null, null, errorMessage);
        }
    }
    
    public static class AnalysisProgress {
        private final int percent;
        private final String message;
        
        public AnalysisProgress(int percent, String message) {
            this.percent = percent;
            this.message = message;
        }
        
        public int getPercent() { return percent; }
        public String getMessage() { return message; }
    }
}
