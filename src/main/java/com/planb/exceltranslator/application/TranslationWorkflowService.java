package com.planb.exceltranslator.application;

import com.planb.exceltranslator.domain.model.TranslationRequest;
import com.planb.exceltranslator.domain.model.TranslationResult;
import javafx.concurrent.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Consumer;

public class TranslationWorkflowService {
    
    private static final Logger logger = LoggerFactory.getLogger(TranslationWorkflowService.class);
    private static final String TRANSLATION_CANCELLED = "Translation cancelled";
    private static final String TRANSLATION_FAILED = "Translation failed: ";
    
    private final TranslationApplicationService translationService;
    private Consumer<String> logCallback;
    private Consumer<WorkflowProgress> progressCallback;
    private Task<TranslationResult> currentTask;
    
    public TranslationWorkflowService(TranslationApplicationService translationService) {
        this.translationService = translationService;
        logger.info("Translation Workflow Service initialized");
    }
    
    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
    
    public void setProgressCallback(Consumer<WorkflowProgress> progressCallback) {
        this.progressCallback = progressCallback;
    }
    
    public TranslationTask startTranslation(TranslationRequest request) {
        if (currentTask != null && currentTask.isRunning()) {
            throw new IllegalStateException("Translation is already in progress");
        }
        
        logMessage("========== TRANSLATION STARTED ==========");
        logMessage("Starting translation: " + request.toString());
        
        currentTask = createTranslationTask(request);
        
        Thread translationThread = new Thread(currentTask);
        translationThread.setDaemon(true);
        translationThread.start();
        
        updateProgress(WorkflowProgress.Status.STARTED, 0, "Translation started");
        
        return new TranslationTask(currentTask);
    }
    
    /**
     * Cancels current translation
     */
    public boolean cancelTranslation() {
        if (currentTask != null && currentTask.isRunning()) {
            currentTask.cancel(true);
            logMessage(TRANSLATION_CANCELLED + " by user");
            updateProgress(WorkflowProgress.Status.CANCELLED, 0, TRANSLATION_CANCELLED);
            return true;
        }
        return false;
    }
    
    /**
     * Checks if translation is currently running
     */
    public boolean isTranslationRunning() {
        return currentTask != null && currentTask.isRunning();
    }
    
    /**
     * Gets current translation task
     */
    public TranslationTask getCurrentTask() {
        return currentTask != null ? new TranslationTask(currentTask) : null;
    }
    
    private Task<TranslationResult> createTranslationTask(TranslationRequest request) {
        return new Task<TranslationResult>() {
            @Override
            protected TranslationResult call() throws Exception {
                return translationService.translate(request);
            }
            
            @Override
            protected void succeeded() {
                TranslationResult result = getValue();
                logMessage("Translation task completed successfully");
                
                if (result.isSuccess()) {
                    TranslationWorkflowService.this.updateProgress(WorkflowProgress.Status.COMPLETED, 100, 
                                 "Translation completed: " + result.getSummaryMessage());
                    logMessage("Translation completed successfully: " + result.getSummaryMessage());
                    logMessage("========== TRANSLATION COMPLETED ==========");
                } else {
                    TranslationWorkflowService.this.updateProgress(WorkflowProgress.Status.FAILED, 0, 
                                 TRANSLATION_FAILED + String.join(", ", result.getErrors()));
                    logMessage(TRANSLATION_FAILED + result.getErrors().size() + " errors");
                    logMessage("========== TRANSLATION FAILED ==========");
                }
            }
            
            @Override
            protected void failed() {
                Throwable exception = getException();
                logger.error("Translation task failed", exception);
                String errorMessage = exception != null ? exception.getMessage() : "Unknown error";
                logMessage(TRANSLATION_FAILED + errorMessage);
                TranslationWorkflowService.this.updateProgress(WorkflowProgress.Status.FAILED, 0, TRANSLATION_FAILED + errorMessage);
                logMessage("========== TRANSLATION FAILED ==========");
            }
            
            @Override
            protected void cancelled() {
                logMessage(TRANSLATION_CANCELLED);
                TranslationWorkflowService.this.updateProgress(WorkflowProgress.Status.CANCELLED, 0, TRANSLATION_CANCELLED);
                logMessage("========== TRANSLATION CANCELLED ==========");
            }
        };
    }
    
    private void logMessage(String message) {
        if (logCallback != null) {
            logCallback.accept(message);
        }
        logger.info("Translation Workflow: {}", message);
    }
    
    private void updateProgress(WorkflowProgress.Status status, int percent, String message) {
        if (progressCallback != null) {
            progressCallback.accept(new WorkflowProgress(status, percent, message));
        }
    }
    
    public void cleanup() {
        if (currentTask != null && currentTask.isRunning()) {
            currentTask.cancel(true);
        }
        logger.info("Translation Workflow Service cleanup completed");
    }
    
    // Inner classes for data transfer
    public static class TranslationTask {
        private final Task<TranslationResult> internalTask;
        
        public TranslationTask(Task<TranslationResult> task) {
            this.internalTask = task;
        }
        
        public boolean isRunning() {
            return internalTask != null && internalTask.isRunning();
        }
        
        public boolean isDone() {
            return internalTask != null && internalTask.isDone();
        }
        
        public boolean isCancelled() {
            return internalTask != null && internalTask.isCancelled();
        }
        
        public TranslationResult getResult() {
            if (internalTask != null && internalTask.isDone() && !internalTask.isCancelled()) {
                try {
                    return internalTask.get();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.error("Thread interrupted while getting translation result", e);
                    return null;
                } catch (Exception e) {
                    logger.error("Failed to get translation result", e);
                    return null;
                }
            }
            return null;
        }
        
        public boolean cancel() {
            return internalTask != null && internalTask.cancel(true);
        }
    }
    
    public static class WorkflowProgress {
        private final Status status;
        private final int percent;
        private final String message;
        
        public WorkflowProgress(Status status, int percent, String message) {
            this.status = status;
            this.percent = percent;
            this.message = message;
        }
        
        public Status getStatus() { return status; }
        public int getPercent() { return percent; }
        public String getMessage() { return message; }
        
        public enum Status {
            STARTED, IN_PROGRESS, COMPLETED, FAILED, CANCELLED
        }
    }
}
