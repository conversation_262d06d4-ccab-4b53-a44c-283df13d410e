package com.planb.exceltranslator.application;

import com.planb.exceltranslator.domain.model.ExcelSheet;
import com.planb.exceltranslator.domain.model.Language;
import com.planb.exceltranslator.domain.model.TranslatableCell;
import com.planb.exceltranslator.domain.model.TranslatorType;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.translation.TranslationServiceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

public class LanguageDetectionService {
    
    private static final Logger logger = LoggerFactory.getLogger(LanguageDetectionService.class);
    private static final int MAX_SAMPLES = 5;
    
    private final TranslationServiceFactory translationServiceFactory;
    private Consumer<String> logCallback;
    
    public LanguageDetectionService(ConfigurationManager configManager) {
        this.translationServiceFactory = new TranslationServiceFactory(configManager);
        logger.info("Language Detection Service initialized");
    }
    
    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
    
    public CompletableFuture<LanguageDetectionResult> detectLanguageAsync(List<ExcelSheet> sheets, 
                                                                          TranslatorType currentTranslator) {
        if (sheets == null || sheets.isEmpty()) {
            return CompletableFuture.completedFuture(
                LanguageDetectionResult.noDetection("No sheets available for language detection"));
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                String sampleText = extractSampleText(sheets);
                
                if (sampleText.trim().isEmpty()) {
                    logMessage("Language detection: No text content found in sheets");
                    return LanguageDetectionResult.noDetection("No text content found for analysis");
                }
                
                logMessage("Analyzing language from sample text: " + sampleText.substring(0, Math.min(50, sampleText.length())) + "...");
                
                // Try to detect language using available translators
                Language detectedLanguage = performDetection(sampleText, currentTranslator);
                
                if (detectedLanguage != null && detectedLanguage != Language.AUTO_DETECT) {
                    logMessage("Detected language: " + detectedLanguage.getEnglishName());
                    return LanguageDetectionResult.success(detectedLanguage);
                } else {
                    logMessage("Language detection: Unable to determine source language");
                    return LanguageDetectionResult.noDetection("Unable to determine source language");
                }
                
            } catch (Exception e) {
                logger.error("Error during language detection", e);
                logMessage("Language detection failed: " + e.getMessage());
                return LanguageDetectionResult.error("Language detection failed: " + e.getMessage());
            }
        });
    }
    
    /**
     * Extracts sample text from Excel sheets for language detection
     */
    private String extractSampleText(List<ExcelSheet> sheets) {
        StringBuilder sampleText = new StringBuilder();
        int samplesCollected = 0;

        for (ExcelSheet sheet : sheets) {
            samplesCollected = extractFromSheet(sheet, sampleText, samplesCollected);
            if (samplesCollected >= MAX_SAMPLES) {
                break;
            }
        }
        
        return sampleText.toString();
    }
    
    private int extractFromSheet(ExcelSheet sheet, StringBuilder sampleText, int currentSamples) {
        int samplesCollected = currentSamples;
        
        for (TranslatableCell cell : sheet.getTranslatableCells()) {
            if (samplesCollected >= MAX_SAMPLES) {
                break;
            }

            String cellText = cell.getOriginalText();
            if (isValidCellText(cellText)) {
                appendCellText(sampleText, cellText);
                samplesCollected++;
            }
        }
        
        return samplesCollected;
    }
    
    private boolean isValidCellText(String cellText) {
        return cellText != null && cellText.trim().length() > 3;
    }
    
    private void appendCellText(StringBuilder sampleText, String cellText) {
        if (!sampleText.isEmpty()) {
            sampleText.append(" ");
        }
        sampleText.append(cellText.trim());
    }
    
    /**
     * Performs language detection with fallback to other translators
     */
    private Language performDetection(String sampleText, TranslatorType currentTranslator) {
        // First try with current translator
        if (currentTranslator != null) {
            Language result = tryDetectWithTranslator(sampleText, currentTranslator);
            if (result != null && result != Language.AUTO_DETECT) {
                return result;
            }
        }

        // If current translator failed, try the other ones
        for (TranslatorType fallbackTranslator : TranslatorType.values()) {
            if (fallbackTranslator != currentTranslator) {
                Language result = tryDetectWithTranslator(sampleText, fallbackTranslator);
                if (result != null && result != Language.AUTO_DETECT) {
                    logger.debug("Language detection succeeded with fallback translator: {}", fallbackTranslator);
                    return result;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Attempts language detection with a specific translator
     */
    private Language tryDetectWithTranslator(String sampleText, TranslatorType translatorType) {
        try {
            var translationService = translationServiceFactory.createTranslationService(translatorType);
            return translationService.detectLanguage(sampleText);
        } catch (Exception e) {
            logger.debug("Language detection failed with {}: {}", translatorType, e.getMessage());
            return null;
        }
    }
    
    /**
     * Creates auto-detection result with suggested language changes
     */
    public AutoDetectionSuggestion createAutoDetectionSuggestion(Language detectedLanguage, 
                                                               Language currentSource, 
                                                               Language currentTarget,
                                                               UIService uiService) {
        AutoDetectionSuggestion.Builder builder = AutoDetectionSuggestion.builder()
                .detectedLanguage(detectedLanguage);
        
        // Check if we should update source language
        if (currentSource == Language.AUTO_DETECT) {
            builder.suggestSourceLanguage(detectedLanguage)
                   .sourceChangeReason("Auto-Detected");
        }
        
        // Check if source and target languages are the same after auto-detection
        if (detectedLanguage == currentTarget) {
            Language newTargetLanguage = uiService.getAlternativeTargetLanguage(detectedLanguage);
            builder.suggestTargetLanguage(newTargetLanguage)
                   .targetChangeReason("Avoid conflict with detected source language");
        }
        
        return builder.build();
    }
    
    private void logMessage(String message) {
        if (logCallback != null) {
            logCallback.accept(message);
        }
        logger.info("Language Detection: {}", message);
    }
    
    public void cleanup() {
        logger.info("Language Detection Service cleanup completed");
    }
    
    // Inner classes for data transfer
    public static class LanguageDetectionResult {
        private final boolean success;
        private final Language detectedLanguage;
        private final String message;
        private final DetectionStatus status;
        
        private LanguageDetectionResult(DetectionStatus status, Language detectedLanguage, String message) {
            this.status = status;
            this.success = status == DetectionStatus.SUCCESS;
            this.detectedLanguage = detectedLanguage;
            this.message = message;
        }
        
        public boolean isSuccess() { return success; }
        public Language getDetectedLanguage() { return detectedLanguage; }
        public String getMessage() { return message; }
        public DetectionStatus getStatus() { return status; }
        
        public static LanguageDetectionResult success(Language language) {
            return new LanguageDetectionResult(DetectionStatus.SUCCESS, language, "Language detected successfully");
        }
        
        public static LanguageDetectionResult noDetection(String reason) {
            return new LanguageDetectionResult(DetectionStatus.NO_DETECTION, null, reason);
        }
        
        public static LanguageDetectionResult error(String errorMessage) {
            return new LanguageDetectionResult(DetectionStatus.ERROR, null, errorMessage);
        }
        
        public enum DetectionStatus {
            SUCCESS, NO_DETECTION, ERROR
        }
    }
    
    public static class AutoDetectionSuggestion {
        private final Language detectedLanguage;
        private final Language suggestedSourceLanguage;
        private final Language suggestedTargetLanguage;
        private final String sourceChangeReason;
        private final String targetChangeReason;
        
        private AutoDetectionSuggestion(Builder builder) {
            this.detectedLanguage = builder.detectedLanguage;
            this.suggestedSourceLanguage = builder.suggestedSourceLanguage;
            this.suggestedTargetLanguage = builder.suggestedTargetLanguage;
            this.sourceChangeReason = builder.sourceChangeReason;
            this.targetChangeReason = builder.targetChangeReason;
        }
        
        // Getters
        public Language getDetectedLanguage() { return detectedLanguage; }
        public Language getSuggestedSourceLanguage() { return suggestedSourceLanguage; }
        public Language getSuggestedTargetLanguage() { return suggestedTargetLanguage; }
        public String getSourceChangeReason() { return sourceChangeReason; }
        public String getTargetChangeReason() { return targetChangeReason; }
        
        public boolean hasSuggestedSourceChange() { return suggestedSourceLanguage != null; }
        public boolean hasSuggestedTargetChange() { return suggestedTargetLanguage != null; }
        
        public static Builder builder() {
            return new Builder();
        }
        
        public static class Builder {
            private Language detectedLanguage;
            private Language suggestedSourceLanguage;
            private Language suggestedTargetLanguage;
            private String sourceChangeReason;
            private String targetChangeReason;
            
            public Builder detectedLanguage(Language detectedLanguage) {
                this.detectedLanguage = detectedLanguage;
                return this;
            }
            
            public Builder suggestSourceLanguage(Language suggestedSourceLanguage) {
                this.suggestedSourceLanguage = suggestedSourceLanguage;
                return this;
            }
            
            public Builder suggestTargetLanguage(Language suggestedTargetLanguage) {
                this.suggestedTargetLanguage = suggestedTargetLanguage;
                return this;
            }
            
            public Builder sourceChangeReason(String sourceChangeReason) {
                this.sourceChangeReason = sourceChangeReason;
                return this;
            }
            
            public Builder targetChangeReason(String targetChangeReason) {
                this.targetChangeReason = targetChangeReason;
                return this;
            }
            
            public AutoDetectionSuggestion build() {
                return new AutoDetectionSuggestion(this);
            }
        }
    }
}
