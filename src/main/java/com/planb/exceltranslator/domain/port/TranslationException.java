package com.planb.exceltranslator.domain.port;

/**
 * Exception thrown when translation operations fail
 */
public class TranslationException extends Exception {
    
    private final ErrorType errorType;
    private final String userFriendlyMessage;
    
    public TranslationException(String message) {
        this(message, ErrorType.GENERAL, null, null);
    }
    
    public TranslationException(String message, Throwable cause) {
        this(message, ErrorType.GENERAL, null, cause);
    }
    
    public TranslationException(String message, ErrorType errorType) {
        this(message, errorType, null, null);
    }
    
    public TranslationException(String message, ErrorType errorType, String userFriendlyMessage) {
        this(message, errorType, userFriendlyMessage, null);
    }
    
    public TranslationException(String message, ErrorType errorType, String userFriendlyMessage, Throwable cause) {
        super(message, cause);
        this.errorType = errorType != null ? errorType : ErrorType.GENERAL;
        this.userFriendlyMessage = userFriendlyMessage != null ? userFriendlyMessage : message;
    }
    
    public ErrorType getErrorType() {
        return errorType;
    }
    
    public String getUserFriendlyMessage() {
        return userFriendlyMessage;
    }
    
    /**
     * Types of translation errors for better error handling and user feedback
     */
    public enum ErrorType {
        GENERAL("General error"),
        API_KEY_INVALID("Invalid API key"),
        API_KEY_MISSING("Missing API key"),
        NETWORK_ERROR("Network connection error"),
        RATE_LIMIT_EXCEEDED("Rate limit exceeded"),
        UNSUPPORTED_LANGUAGE("Unsupported language"),
        TEXT_TOO_LONG("Text too long for translation"),
        SERVICE_UNAVAILABLE("Translation service unavailable"),
        QUOTA_EXCEEDED("Translation quota exceeded"),
        INVALID_REQUEST("Invalid translation request"),
        TIMEOUT("Translation request timed out");
        
        private final String description;
        
        ErrorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
        
        /**
         * Gets a user-friendly message for the error type
         */
        public String getUserFriendlyMessage() {
            switch (this) {
                case API_KEY_INVALID:
                    return "The API key is invalid. Please check your configuration.";
                case API_KEY_MISSING:
                    return "API key is missing. Please configure your API credentials.";
                case NETWORK_ERROR:
                    return "Unable to connect to the translation service. Please check your internet connection.";
                case RATE_LIMIT_EXCEEDED:
                    return "Translation rate limit exceeded. Please wait a moment and try again.";
                case UNSUPPORTED_LANGUAGE:
                    return "The selected language combination is not supported by this translator.";
                case TEXT_TOO_LONG:
                    return "The text is too long for translation. Please try with smaller batches.";
                case SERVICE_UNAVAILABLE:
                    return "The translation service is currently unavailable. Please try again later.";
                case QUOTA_EXCEEDED:
                    return "Translation quota has been exceeded. Please check your account limits.";
                case INVALID_REQUEST:
                    return "Invalid translation request. Please check your input.";
                case TIMEOUT:
                    return "Translation request timed out. Please try again.";
                default:
                    return "An error occurred during translation. Please try again.";
            }
        }
    }
}
