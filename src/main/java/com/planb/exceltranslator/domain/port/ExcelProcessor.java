package com.planb.exceltranslator.domain.port;

import com.planb.exceltranslator.domain.model.ExcelSheet;
import com.planb.exceltranslator.domain.model.TranslationResult;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * Port interface for Excel processing operations
 * This interface defines the contract for Excel file handling
 */
public interface ExcelProcessor {
    
    /**
     * Reads an Excel file and extracts all sheets with translatable cells
     * 
     * @param excelFile the Excel file to read (.xlsx or .xls)
     * @return list of Excel sheets with translatable cells
     * @throws IOException if file reading fails
     * @throws UnsupportedOperationException if file format is not supported
     */
    List<ExcelSheet> readExcelFile(File excelFile) throws IOException;
    
    /**
     * Writes the translated content back to a new Excel file
     * 
     * @param originalFile the original Excel file
     * @param translatedSheets the sheets with translated content
     * @param outputFile the output file to write
     * @throws IOException if file writing fails
     */
    void writeTranslatedExcel(File originalFile, List<ExcelSheet> translatedSheets, File outputFile) 
            throws IOException;
    
    /**
     * Validates if the file is a supported Excel format
     * 
     * @param file the file to validate
     * @return true if the file is a supported Excel format (.xlsx or .xls)
     */
    boolean isSupportedExcelFile(File file);
    
    /**
     * Gets the file extension for the given Excel file
     * 
     * @param file the Excel file
     * @return the file extension (.xlsx or .xls)
     */
    String getFileExtension(File file);
    
    /**
     * Estimates the processing time for the given Excel file
     * 
     * @param excelFile the Excel file to analyze
     * @return estimated processing time in seconds
     * @throws IOException if file analysis fails
     */
    int estimateProcessingTime(File excelFile) throws IOException;
    
    /**
     * Gets basic information about the Excel file
     * 
     * @param excelFile the Excel file to analyze
     * @return file information including sheet count, cell count, etc.
     * @throws IOException if file analysis fails
     */
    ExcelFileInfo getFileInfo(File excelFile) throws IOException;
    
    /**
     * Creates a backup of the original file before processing
     * 
     * @param originalFile the file to backup
     * @return the backup file
     * @throws IOException if backup creation fails
     */
    File createBackup(File originalFile) throws IOException;
    
    /**
     * Information about an Excel file
     */
    class ExcelFileInfo {
        private final String fileName;
        private final long fileSize;
        private final int sheetCount;
        private final int totalCells;
        private final int translatableCells;
        private final String fileFormat;
        
        public ExcelFileInfo(String fileName, long fileSize, int sheetCount, 
                int totalCells, int translatableCells, String fileFormat) {
            this.fileName = fileName;
            this.fileSize = fileSize;
            this.sheetCount = sheetCount;
            this.totalCells = totalCells;
            this.translatableCells = translatableCells;
            this.fileFormat = fileFormat;
        }
        
        public String getFileName() { return fileName; }
        public long getFileSize() { return fileSize; }
        public int getSheetCount() { return sheetCount; }
        public int getTotalCells() { return totalCells; }
        public int getTranslatableCells() { return translatableCells; }
        public String getFileFormat() { return fileFormat; }
        
        /**
         * Gets a human-readable file size string
         */
        public String getFormattedFileSize() {
            if (fileSize < 1024) {
                return fileSize + " B";
            } else if (fileSize < 1024 * 1024) {
                return String.format("%.1f KB", fileSize / 1024.0);
            } else {
                return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
            }
        }
        
        @Override
        public String toString() {
            return String.format("ExcelFileInfo{fileName='%s', size=%s, sheets=%d, translatableCells=%d, format='%s'}", 
                    fileName, getFormattedFileSize(), sheetCount, translatableCells, fileFormat);
        }
    }
}
