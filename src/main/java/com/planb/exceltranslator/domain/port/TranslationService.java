package com.planb.exceltranslator.domain.port;

import com.planb.exceltranslator.domain.model.Language;
import com.planb.exceltranslator.domain.model.TranslatorType;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Port interface for translation services following hexagonal architecture
 * This interface defines the contract for translation adapters
 */
public interface TranslationService {

        /**
         * Gets the translator type this service implements
         */
        TranslatorType getTranslatorType();

        /**
         * Translates a single text from source to target language
         * 
         * @param text           the text to translate
         * @param sourceLanguage the source language (can be AUTO_DETECT)
         * @param targetLanguage the target language
         * @return the translated text
         * @throws TranslationException if translation fails
         */
        String translate(String text, Language sourceLanguage, Language targetLanguage)
                        throws TranslationException;

        /**
         * Translates a batch of texts from source to target language
         * This method is optimized for bulk translation to reduce API calls
         * 
         * @param texts          the list of texts to translate
         * @param sourceLanguage the source language (can be AUTO_DETECT)
         * @param targetLanguage the target language
         * @return the list of translated texts in the same order
         * @throws TranslationException if translation fails
         */
        List<String> translateBatch(List<String> texts, Language sourceLanguage, Language targetLanguage)
                        throws TranslationException;

        /**
         * Translates a batch of texts asynchronously
         * 
         * @param texts          the list of texts to translate
         * @param sourceLanguage the source language (can be AUTO_DETECT)
         * @param targetLanguage the target language
         * @return a CompletableFuture containing the list of translated texts
         */
        CompletableFuture<List<String>> translateBatchAsync(List<String> texts,
                        Language sourceLanguage,
                        Language targetLanguage);

        /**
         * Detects the language of the given text
         * 
         * @param text the text to analyze
         * @return the detected language
         * @throws TranslationException if language detection fails
         */
        Language detectLanguage(String text) throws TranslationException;

        /**
         * Checks if the service is available and properly configured
         * 
         * @return true if the service is available, false otherwise
         */
        boolean isAvailable();

        /**
         * Gets the maximum batch size supported by this service
         * 
         * @return the maximum number of texts that can be translated in a single batch
         */
        int getMaxBatchSize();

        /**
         * Gets the rate limit for this service (requests per second)
         * 
         * @return the maximum number of requests per second
         */
        int getRateLimit();

        /**
         * Gets the supported source languages for this service
         * 
         * @return list of supported source languages
         */
        List<Language> getSupportedSourceLanguages();

        /**
         * Gets the supported target languages for this service
         * 
         * @return list of supported target languages
         */
        List<Language> getSupportedTargetLanguages();

        /**
         * Validates if the language pair is supported by this service
         * 
         * @param sourceLanguage the source language
         * @param targetLanguage the target language
         * @return true if the language pair is supported
         */
        boolean isLanguagePairSupported(Language sourceLanguage, Language targetLanguage);

        /**
         * Performs a health check on the translation service
         * This includes checking API credentials, connectivity, and service status
         * 
         * @throws TranslationException if the health check fails
         */
        void healthCheck() throws TranslationException;
}
