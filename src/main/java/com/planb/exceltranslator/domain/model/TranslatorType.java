package com.planb.exceltranslator.domain.model;

/**
 * Enumeration representing supported translation services
 */
public enum TranslatorType {
    DEEPL("DeepL Translator (Recommended)", "DeepL翻訳", "DeepL Translator", true),
    GOOGLE("Google Translator", "Google翻訳", "Google Translator", false);
    
    private final String englishName;
    private final String japaneseName;
    private final String vietnameseName;
    private final boolean isDefault;
    
    TranslatorType(String englishName, String japaneseName, String vietnameseName, boolean isDefault) {
        this.englishName = englishName;
        this.japaneseName = japaneseName;
        this.vietnameseName = vietnameseName;
        this.isDefault = isDefault;
    }
    
    public String getEnglishName() {
        return englishName;
    }
    
    public String getJapaneseName() {
        return japaneseName;
    }
    
    public String getVietnameseName() {
        return vietnameseName;
    }
    
    public boolean isDefault() {
        return isDefault;
    }
    
    public String getLocalizedName(Language uiLanguage) {
        switch (uiLanguage) {
            case JAPANESE:
                return japaneseName;
            case VIETNAMESE:
                return vietnameseName;
            default:
                return englishName;
        }
    }
    
    public static TranslatorType getDefault() {
        for (TranslatorType type : values()) {
            if (type.isDefault) {
                return type;
            }
        }
        return DEEPL;
    }
    
    @Override
    public String toString() {
        return englishName;
    }
}
