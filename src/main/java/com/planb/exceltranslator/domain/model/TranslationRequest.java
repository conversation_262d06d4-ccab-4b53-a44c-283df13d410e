package com.planb.exceltranslator.domain.model;

import java.io.File;
import java.util.List;
import java.util.Objects;

/**
 * Represents a translation request containing all necessary information
 * for translating an Excel file
 */
public class TranslationRequest {
    private final File excelFile;
    private final Language sourceLanguage;
    private final Language targetLanguage;
    private final TranslatorType translatorType;
    private final int batchSize;
    private final List<ExcelSheet> selectedSheets;
    
    private TranslationRequest(Builder builder) {
        this.excelFile = Objects.requireNonNull(builder.excelFile, "Excel file cannot be null");
        this.sourceLanguage = Objects.requireNonNull(builder.sourceLanguage, "Source language cannot be null");
        this.targetLanguage = Objects.requireNonNull(builder.targetLanguage, "Target language cannot be null");
        this.translatorType = Objects.requireNonNull(builder.translatorType, "Translator type cannot be null");
        this.batchSize = builder.batchSize;
        this.selectedSheets = Objects.requireNonNull(builder.selectedSheets, "Selected sheets cannot be null");
        
        // Validation
        if (batchSize <= 0) {
            throw new IllegalArgumentException("Batch size must be positive");
        }
        
        if (selectedSheets.isEmpty()) {
            throw new IllegalArgumentException("At least one sheet must be selected");
        }
        
        if (sourceLanguage == targetLanguage && sourceLanguage != Language.AUTO_DETECT) {
            throw new IllegalArgumentException("Source and target languages cannot be the same");
        }
    }
    
    public File getExcelFile() {
        return excelFile;
    }
    
    public Language getSourceLanguage() {
        return sourceLanguage;
    }
    
    public Language getTargetLanguage() {
        return targetLanguage;
    }
    
    public TranslatorType getTranslatorType() {
        return translatorType;
    }
    
    public int getBatchSize() {
        return batchSize;
    }
    
    public List<ExcelSheet> getSelectedSheets() {
        return selectedSheets;
    }
    
    /**
     * Gets the total number of translatable cells across all selected sheets
     */
    public int getTotalTranslatableCells() {
        return selectedSheets.stream()
                .filter(ExcelSheet::isSelected)
                .mapToInt(ExcelSheet::getTranslatableCellCount)
                .sum();
    }
    
    /**
     * Estimates the number of API requests needed based on batch size
     */
    public int getEstimatedApiRequests() {
        int totalCells = getTotalTranslatableCells();
        return (int) Math.ceil((double) totalCells / batchSize);
    }
    
    /**
     * Estimates the processing time based on API rate limits
     * Assumes 5 requests per second for free plans
     */
    public int getEstimatedProcessingTimeSeconds() {
        int requests = getEstimatedApiRequests();
        return (int) Math.ceil((double) requests / 5.0); // 5 requests per second
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        private File excelFile;
        private Language sourceLanguage = Language.AUTO_DETECT;
        private Language targetLanguage;
        private TranslatorType translatorType = TranslatorType.getDefault();
        private int batchSize = 50;
        private List<ExcelSheet> selectedSheets;
        
        public Builder excelFile(File excelFile) {
            this.excelFile = excelFile;
            return this;
        }
        
        public Builder sourceLanguage(Language sourceLanguage) {
            this.sourceLanguage = sourceLanguage;
            return this;
        }
        
        public Builder targetLanguage(Language targetLanguage) {
            this.targetLanguage = targetLanguage;
            return this;
        }
        
        public Builder translatorType(TranslatorType translatorType) {
            this.translatorType = translatorType;
            return this;
        }
        
        public Builder batchSize(int batchSize) {
            this.batchSize = batchSize;
            return this;
        }
        
        public Builder selectedSheets(List<ExcelSheet> selectedSheets) {
            this.selectedSheets = selectedSheets;
            return this;
        }
        
        public TranslationRequest build() {
            return new TranslationRequest(this);
        }
    }
    
    @Override
    public String toString() {
        return String.format("TranslationRequest{file='%s', %s->%s, translator=%s, batchSize=%d, sheets=%d}", 
                excelFile.getName(), sourceLanguage.getCode(), targetLanguage.getCode(), 
                translatorType, batchSize, selectedSheets.size());
    }
}
