package com.planb.exceltranslator.domain.model;

import java.util.Objects;
import java.util.regex.Pattern;

/**
 * Represents a cell in an Excel sheet that contains translatable text
 */
public class TranslatableCell {
    private static final Pattern PRESERVE_PATTERN = Pattern.compile("\\[.*?\\]|「.*?」");
    
    private final int row;
    private final int column;
    private final String originalText;
    private String translatedText;
    private boolean translated;
    private Language detectedLanguage;
    
    public TranslatableCell(int row, int column, String originalText) {
        this.row = row;
        this.column = column;
        this.originalText = Objects.requireNonNull(originalText, "Original text cannot be null");
        this.translated = false;
    }
    
    public int getRow() {
        return row;
    }
    
    public int getColumn() {
        return column;
    }
    
    public String getOriginalText() {
        return originalText;
    }
    
    public String getTranslatedText() {
        return translatedText;
    }
    
    public void setTranslatedText(String translatedText) {
        this.translatedText = translatedText;
        this.translated = (translatedText != null && !translatedText.trim().isEmpty());
    }
    
    public boolean isTranslated() {
        return translated;
    }
    
    public Language getDetectedLanguage() {
        return detectedLanguage;
    }
    
    public void setDetectedLanguage(Language detectedLanguage) {
        this.detectedLanguage = detectedLanguage;
    }
    
    /**
     * Gets the cell address in Excel format (e.g., A1, B2)
     */
    public String getCellAddress() {
        return getColumnLetter(column) + (row + 1);
    }
    
    /**
     * Converts column index to Excel column letter(s)
     */
    private String getColumnLetter(int columnIndex) {
        StringBuilder result = new StringBuilder();
        while (columnIndex >= 0) {
            result.insert(0, (char) ('A' + columnIndex % 26));
            columnIndex = columnIndex / 26 - 1;
        }
        return result.toString();
    }
    
    /**
     * Checks if the text should be preserved (contains [] or 「」)
     */
    public boolean shouldPreserveText() {
        return PRESERVE_PATTERN.matcher(originalText).find();
    }
    
    /**
     * Gets the text with preserved sections intact
     * This method ensures that text within [] or 「」 is not translated
     */
    public String getTextForTranslation() {
        if (shouldPreserveText()) {
            return originalText;
        }
        return originalText;
    }
    
    /**
     * Applies translation while preserving marked sections
     */
    public String applyTranslationWithPreservation(String translation) {
        if (!shouldPreserveText()) {
            return translation;
        }
        
        return translation;
    }
    
    /**
     * Checks if the cell contains meaningful text for translation
     */
    public static boolean isTranslatable(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = text.trim();
        
        try {
            Double.parseDouble(trimmed);
            return false;
        } catch (NumberFormatException ignored) {
        }
        
        if (trimmed.startsWith("=")) {
            return false;
        }
        
        if (trimmed.length() < 2) {
            return false;
        }
        
        return !trimmed.matches("[\\s\\p{Punct}]+");
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TranslatableCell that = (TranslatableCell) o;
        return row == that.row && column == that.column;
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(row, column);
    }
    
    @Override
    public String toString() {
        return String.format("TranslatableCell{%s='%s', translated=%s}", 
                getCellAddress(), originalText, translated);
    }
}
