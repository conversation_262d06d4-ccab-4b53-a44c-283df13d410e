package com.planb.exceltranslator.domain.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Represents the result of a translation operation
 */
public class TranslationResult {
    private final TranslationRequest request;
    private final boolean success;
    private final List<ExcelSheet> translatedSheets;
    private final List<String> errors;
    private final List<String> warnings;
    private final LocalDateTime startTime;
    private final LocalDateTime endTime;
    private final TranslationStatistics statistics;
    
    private TranslationResult(Builder builder) {
        this.request = Objects.requireNonNull(builder.request, "Translation request cannot be null");
        this.success = builder.success;
        this.translatedSheets = new ArrayList<>(builder.translatedSheets);
        this.errors = new ArrayList<>(builder.errors);
        this.warnings = new ArrayList<>(builder.warnings);
        this.startTime = Objects.requireNonNull(builder.startTime, "Start time cannot be null");
        this.endTime = builder.endTime;
        this.statistics = builder.statistics;
    }
    
    public TranslationRequest getRequest() {
        return request;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public List<ExcelSheet> getTranslatedSheets() {
        return new ArrayList<>(translatedSheets);
    }
    
    public List<String> getErrors() {
        return new ArrayList<>(errors);
    }
    
    public List<String> getWarnings() {
        return new ArrayList<>(warnings);
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public TranslationStatistics getStatistics() {
        return statistics;
    }
    
    /**
     * Gets the total processing time in seconds
     */
    public long getProcessingTimeSeconds() {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return java.time.Duration.between(startTime, endTime).getSeconds();
    }
    
    /**
     * Checks if there are any errors
     */
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    /**
     * Checks if there are any warnings
     */
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    /**
     * Gets a summary message for the translation result
     */
    public String getSummaryMessage() {
        if (success) {
            return String.format("Translation completed successfully in %d seconds. " +
                    "Processed %d sheets with %d cells translated.", 
                    getProcessingTimeSeconds(), 
                    translatedSheets.size(),
                    statistics != null ? statistics.getTotalTranslatedCells() : 0);
        } else {
            return String.format("Translation failed with %d errors. Processing time: %d seconds.", 
                    errors.size(), getProcessingTimeSeconds());
        }
    }
    
    public static Builder builder(TranslationRequest request) {
        return new Builder(request);
    }
    
    public static class Builder {
        private final TranslationRequest request;
        private boolean success = false;
        private final List<ExcelSheet> translatedSheets = new ArrayList<>();
        private final List<String> errors = new ArrayList<>();
        private final List<String> warnings = new ArrayList<>();
        private LocalDateTime startTime = LocalDateTime.now();
        private LocalDateTime endTime;
        private TranslationStatistics statistics;
        
        public Builder(TranslationRequest request) {
            this.request = Objects.requireNonNull(request, "Translation request cannot be null");
        }
        
        public Builder success(boolean success) {
            this.success = success;
            return this;
        }
        
        public Builder addTranslatedSheet(ExcelSheet sheet) {
            if (sheet != null) {
                this.translatedSheets.add(sheet);
            }
            return this;
        }
        
        public Builder addTranslatedSheets(List<ExcelSheet> sheets) {
            if (sheets != null) {
                this.translatedSheets.addAll(sheets);
            }
            return this;
        }
        
        public Builder addError(String error) {
            if (error != null && !error.trim().isEmpty()) {
                this.errors.add(error);
            }
            return this;
        }
        
        public Builder addWarning(String warning) {
            if (warning != null && !warning.trim().isEmpty()) {
                this.warnings.add(warning);
            }
            return this;
        }
        
        public Builder startTime(LocalDateTime startTime) {
            this.startTime = startTime;
            return this;
        }
        
        public Builder endTime(LocalDateTime endTime) {
            this.endTime = endTime;
            return this;
        }
        
        public Builder statistics(TranslationStatistics statistics) {
            this.statistics = statistics;
            return this;
        }
        
        public TranslationResult build() {
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            return new TranslationResult(this);
        }
    }
    
    /**
     * Statistics about the translation operation
     */
    public static class TranslationStatistics {
        private final int totalCells;
        private final int translatedCells;
        private final int skippedCells;
        private final int failedCells;
        private final int apiRequests;
        
        public TranslationStatistics(int totalCells, int translatedCells, int skippedCells,
                int failedCells, int apiRequests) {
            this.totalCells = totalCells;
            this.translatedCells = translatedCells;
            this.skippedCells = skippedCells;
            this.failedCells = failedCells;
            this.apiRequests = apiRequests;
        }
        
        public int getTotalCells() { return totalCells; }
        public int getTotalTranslatedCells() { return translatedCells; }
        public int getSkippedCells() { return skippedCells; }
        public int getFailedCells() { return failedCells; }
        public int getApiRequests() { return apiRequests; }
        
        public double getSuccessRate() {
            return totalCells > 0 ? (double) translatedCells / totalCells * 100.0 : 0.0;
        }
    }
}
