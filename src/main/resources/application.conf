# Excel Translator Application Configuration
# This file contains default configuration values
# Override these values using environment variables or system properties

# API Configuration
deepl {
  api {
    key = "65e0cb37-77da-44b0-b0f0-1b80ca38f9a8:fx"
  }
}

google {
  api {
    key = "AIzaSyCJDFcpGTO8XVTmMI0elsduXCxWpRNwrRU"
  }
}

# Translation Configuration
translation {
  batch {
    size = 50
    max.size = 100
  }
  request {
    timeout.seconds = 30
  }
  rate.limit = 5
}

# File Configuration
file {
  max.size.mb = 50
  temp.directory = ${java.io.tmpdir}
  create.backup = true
}

# Logging Configuration
logging {
  level = "INFO"
}

# UI Configuration
ui {
  language = "en"
}

# Debug Configuration
debug {
  mode = false
}
