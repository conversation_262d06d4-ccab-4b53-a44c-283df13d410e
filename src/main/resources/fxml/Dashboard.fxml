<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Menu?>
<?import javafx.scene.control.MenuBar?>
<?import javafx.scene.control.MenuItem?>
<?import javafx.scene.control.ProgressBar?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Spinner?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.shape.Circle?>
<?import javafx.scene.text.Font?>

<VBox prefHeight="767.0" prefWidth="1014.0" xmlns="http://javafx.com/javafx/24.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.planb.exceltranslator.ui.controller.DashboardController">
   <children>
      <MenuBar>
        <menus>
          <Menu fx:id="fileMenu" mnemonicParsing="false" text="File">
            <items>
              <MenuItem fx:id="exitMenuItem" mnemonicParsing="false" onAction="#handleExit" text="Exit" />
            </items>
          </Menu>
          <Menu fx:id="editMenu" mnemonicParsing="false" text="Edit">
            <items>
              <MenuItem fx:id="preferencesMenuItem" mnemonicParsing="false" onAction="#handlePreferences" text="Preferences..." />
            </items>
          </Menu>
          <Menu fx:id="helpMenu" mnemonicParsing="false" text="Help">
            <items>
              <MenuItem fx:id="aboutMenuItem" mnemonicParsing="false" onAction="#handleAbout" text="About Excel Translator" />
            </items>
          </Menu>
        </menus>
      </MenuBar>
      <HBox prefHeight="735.0" prefWidth="947.0">
         <children>
            <VBox prefHeight="735.0" prefWidth="648.0">
               <children>
                  <AnchorPane prefHeight="321.0" prefWidth="571.0">
                     <children>
                        <Pane fx:id="fileDragAndDrop" layoutX="22.0" layoutY="15.0" prefHeight="210.0" prefWidth="532.0" style="-fx-background-radius: 10px; -fx-background-color: #E9E9E9; -fx-border-color: #0098D1; -fx-border-width: 2px; -fx-border-radius: 10px; -fx-border-style: dashed;" AnchorPane.bottomAnchor="20.0" AnchorPane.leftAnchor="20.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="20.0">
                           <children>
                              <Label fx:id="selectedFile" alignment="CENTER" contentDisplay="CENTER" layoutX="163.0" layoutY="62.0" prefHeight="49.0" prefWidth="273.0" text="Drag and drop your Excel file here" textAlignment="CENTER" wrapText="true">
                                 <font>
                                    <Font size="18.0" />
                                 </font>
                              </Label>
                              <Button fx:id="selectFileButton" layoutX="220.0" layoutY="148.0" mnemonicParsing="false" onAction="#selectFileButtonClick" prefHeight="40.0" prefWidth="160.0" style="-fx-background-radius: 10px; -fx-border-radius: 10px; -fx-border-color: #0098D1;" text="Browse File">
                                 <font>
                                    <Font size="14.0" />
                                 </font>
                              </Button>
                              <Label fx:id="fileSize" alignment="CENTER" contentDisplay="CENTER" layoutX="231.0" layoutY="208.0" prefHeight="17.0" prefWidth="139.0" text="" textAlignment="CENTER" />
                              <Label fx:id="fileSize1" alignment="CENTER" contentDisplay="CENTER" layoutX="230.0" layoutY="112.0" prefHeight="17.0" prefWidth="139.0" text="or click Browse to select" textAlignment="CENTER" textFill="#697281" />
                           </children>
                        </Pane>
                     </children>
                  </AnchorPane>
                  <AnchorPane prefHeight="80.0" prefWidth="640.0">
                     <children>
                        <Pane layoutX="20.0" layoutY="-1.0" prefHeight="52.0" prefWidth="577.0" style="-fx-background-radius: 10px; -fx-background-color: #E9E9E9; -fx-border-color: #E0E0E0; -fx-border-radius: 10px;" AnchorPane.bottomAnchor="1.0" AnchorPane.leftAnchor="20.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="-1.0">
                           <children>
                              <Label fx:id="progressStatus" contentDisplay="CENTER" layoutX="20.0" layoutY="17.0" text="Translating..." textAlignment="CENTER" />
                              <Label fx:id="processCompletionStatus" alignment="CENTER_RIGHT" contentDisplay="RIGHT" layoutX="436.0" layoutY="17.0" nodeOrientation="LEFT_TO_RIGHT" prefHeight="17.0" prefWidth="139.0" text="0/100%" textAlignment="RIGHT" />
                              <ProgressBar fx:id="progressBar" layoutX="19.0" layoutY="38.0" prefHeight="20.0" prefWidth="557.0" progress="0.0" />
                           </children>
                        </Pane>
                     </children>
                  </AnchorPane>
                  <AnchorPane prefHeight="325.0" prefWidth="638.0">
                     <children>
                        <Pane layoutX="20.0" layoutY="19.0" prefHeight="253.0" prefWidth="531.0" style="-fx-background-radius: 10px; -fx-background-color: #E9E9E9; -fx-border-color: #E0E0E0; -fx-border-radius: 10px;" AnchorPane.bottomAnchor="12.0" AnchorPane.leftAnchor="20.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="19.0">
                           <children>
                              <Label fx:id="logsLabel" contentDisplay="CENTER" layoutX="22.0" layoutY="13.0" text="Logs" textAlignment="CENTER">
                                 <font>
                                    <Font size="14.0" />
                                 </font>
                              </Label>
                              <TextArea fx:id="logArea" editable="false" layoutX="21.0" layoutY="39.0" prefHeight="241.0" prefWidth="556.0" style="-fx-control-inner-background: #1C2938; -fx-text-fill: #00FF00; -fx-font-family: 'Courier New'; -fx-font-size: 11px;" wrapText="false" />
                           </children>
                        </Pane>
                     </children>
                  </AnchorPane>
               </children>
            </VBox>
            <VBox prefHeight="725.0" prefWidth="376.0">
               <children>
                  <AnchorPane prefHeight="200.0" prefWidth="334.0">
                     <children>
                        <Pane layoutX="30.0" layoutY="10.0" prefHeight="91.0" prefWidth="140.0" style="-fx-background-radius: 10px; -fx-background-color: #E9E9E9; -fx-border-color: #E0E0E0; -fx-border-radius: 10px;" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="10.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="20.0">
                           <children>
                              <Label fx:id="sourceLanguageLabel" contentDisplay="CENTER" layoutX="24.0" layoutY="11.0" text="Source Language" textAlignment="CENTER">
                                 <font>
                                    <Font size="14.0" />
                                 </font>
                              </Label>
                              <ComboBox fx:id="sourceLanguage" layoutX="23.0" layoutY="39.0" prefHeight="40.0" prefWidth="300.0" promptText="Auto-Detect" visibleRowCount="4" />
                              <Label fx:id="targetLanguageLabel" contentDisplay="CENTER" layoutX="24.0" layoutY="95.0" text="Target Language" textAlignment="CENTER">
                                 <font>
                                    <Font size="14.0" />
                                 </font>
                              </Label>
                              <ComboBox fx:id="targetLanguage" layoutX="23.0" layoutY="122.0" prefHeight="40.0" prefWidth="300.0" promptText="Select  target  language . . ." visibleRowCount="3" />
                           </children>
                        </Pane>
                     </children>
                  </AnchorPane>
                  <AnchorPane prefHeight="200.0" prefWidth="200.0">
                     <children>
                        <Pane layoutX="22.0" layoutY="1.0" prefHeight="234.0" prefWidth="322.0" style="-fx-background-radius: 10px; -fx-background-color: #E9E9E9; -fx-border-color: #E0E0E0; -fx-border-radius: 10px;" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="10.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="20.0">
                           <children>
                              <Label fx:id="translatorLabel" contentDisplay="CENTER" layoutX="24.0" layoutY="11.0" text="Translator" textAlignment="CENTER">
                                 <font>
                                    <Font size="14.0" />
                                 </font>
                              </Label>
                              <ComboBox fx:id="translator" layoutX="23.0" layoutY="39.0" prefHeight="40.0" prefWidth="300.0" promptText="DeepL Translator (Recommended)" visibleRowCount="2" />
                              <Label fx:id="batchSizeLabel" contentDisplay="CENTER" layoutX="24.0" layoutY="95.0" text="Batch Size" textAlignment="CENTER">
                                 <font>
                                    <Font size="14.0" />
                                 </font>
                              </Label>
                              <Spinner fx:id="batchSize" editable="true" layoutX="23.0" layoutY="122.0" prefHeight="40.0" prefWidth="300.0" />
                           </children>
                        </Pane>
                     </children>
                  </AnchorPane>
                  <AnchorPane prefHeight="200.0" prefWidth="299.0">
                     <children>
                        <Pane layoutX="-89.0" layoutY="36.0" prefHeight="234.0" prefWidth="322.0" style="-fx-background-radius: 10px; -fx-background-color: #E9E9E9; -fx-border-color: #E0E0E0; -fx-border-radius: 10px;" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="10.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="20.0">
                           <children>
                              <Label fx:id="sheetDetectedLabel" contentDisplay="CENTER" layoutX="24.0" layoutY="11.0" text="Sheet Detected" textAlignment="CENTER">
                                 <font>
                                    <Font size="14.0" />
                                 </font>
                              </Label>
                              <ScrollPane fx:id="sheetsScrollPane" fitToWidth="true" hbarPolicy="NEVER" layoutX="23.0" layoutY="39.0" prefHeight="123.0" prefWidth="301.0" style="-fx-background-radius: 3px;" vbarPolicy="AS_NEEDED">
                                 <content>
                                    <AnchorPane fx:id="sheetsDetectedList" minHeight="0.0" minWidth="0.0" prefHeight="121.0" prefWidth="283.0" style="-fx-background-radius: 3px;">
                                       <!-- Sheets will be dynamically added here when Excel file is analyzed -->
                                    </AnchorPane>
                                 </content>
                              </ScrollPane>
                           </children>
                        </Pane>
                     </children>
                  </AnchorPane>
                  <AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" prefHeight="126.0" prefWidth="376.0">
                     <children>
                        <HBox prefHeight="63.0" prefWidth="376.0">
                           <children>
                              <AnchorPane prefHeight="63.0" prefWidth="376.0">
                                 <children>
                                    <Button fx:id="translateButton" layoutX="24.0" layoutY="8.0" mnemonicParsing="false" onAction="#translateButtonClick" prefHeight="40.0" prefWidth="160.0" style="-fx-background-radius: 10px; -fx-border-radius: 10px; -fx-border-color: #008828;" text="Translate" AnchorPane.leftAnchor="10.0" AnchorPane.topAnchor="20.0" />
                                    <Button fx:id="cancelButton" layoutX="192.0" layoutY="29.0" mnemonicParsing="false" onAction="#cancelButtonClick" prefHeight="40.0" prefWidth="160.0" style="-fx-background-radius: 10px; -fx-border-radius: 10px;" text="Cancel" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="20.0" />
                                 </children>
                              </AnchorPane>
                           </children>
                        </HBox>
                        <HBox layoutY="63.0" prefHeight="63.0" prefWidth="376.0">
                           <children>
                              <AnchorPane prefHeight="79.0" prefWidth="376.0">
                                 <children>
                                    <Button fx:id="exportButton" layoutX="7.0" layoutY="11.0" mnemonicParsing="false" onAction="#exportButtonClick" prefHeight="40.0" prefWidth="160.0" style="-fx-background-radius: 10px; -fx-border-radius: 10px;" text="Export File" AnchorPane.leftAnchor="10.0" AnchorPane.rightAnchor="20.0" />
                                 </children>
                              </AnchorPane>
                           </children>
                        </HBox>
                     </children>
                  </AnchorPane>
               </children>
            </VBox>
         </children>
      </HBox>
      <AnchorPane prefHeight="40.0" prefWidth="1014.0" style="-fx-background-color: #E0E0E0; -fx-border-color: #d0d0d0; -fx-border-width: 1 0 0 0;">
         <children>
            <HBox alignment="CENTER_LEFT" layoutX="20.0" layoutY="5.0" spacing="15.0" AnchorPane.bottomAnchor="5.0" AnchorPane.leftAnchor="20.0" AnchorPane.topAnchor="5.0">
               <children>
                  <Circle fx:id="statusIndicator" fill="#009060" radius="6.0" />
                  <Label fx:id="status" prefHeight="17.0" text="Ready to Translate" />
               </children>
            </HBox>
            <HBox alignment="CENTER_RIGHT" layoutX="814.0" layoutY="5.0" AnchorPane.bottomAnchor="5.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="5.0">
               <children>
                  <Label alignment="CENTER" contentDisplay="CENTER" text="PLAN-B" />
               </children>
            </HBox>
         </children>
      </AnchorPane>
   </children>
</VBox>
