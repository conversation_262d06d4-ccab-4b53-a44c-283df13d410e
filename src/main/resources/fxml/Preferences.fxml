<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.Tab?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="400.0" prefWidth="600.0" xmlns="http://javafx.com/javafx/24.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.planb.exceltranslator.ui.controller.PreferencesController">
   <children>
      <TabPane layoutX="14.0" layoutY="14.0" prefHeight="320.0" prefWidth="572.0" tabClosingPolicy="UNAVAILABLE" AnchorPane.bottomAnchor="60.0" AnchorPane.leftAnchor="14.0" AnchorPane.rightAnchor="14.0" AnchorPane.topAnchor="14.0">
        <tabs>
          <Tab fx:id="interfaceTab" text="Interface">
            <content>
              <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="286.0" prefWidth="572.0">
                     <children>
                        <Label fx:id="interfaceLanguageLabel" layoutX="30.0" layoutY="40.0" text="Interface Language:">
                           <font>
                              <Font size="14.0" />
                           </font>
                        </Label>
                        <ComboBox fx:id="interfaceLanguageComboBox" layoutX="30.0" layoutY="70.0" prefHeight="35.0" prefWidth="200.0" promptText="Select Language" />
                        <Label fx:id="languageNoteLabel" layoutX="30.0" layoutY="120.0" prefHeight="40.0" prefWidth="520.0" text="Note: Changing the interface language will update all UI elements immediately." textFill="#666666" wrapText="true">
                           <font>
                              <Font size="12.0" />
                           </font>
                        </Label>
                     </children>
                  </AnchorPane>
            </content>
          </Tab>
          <Tab fx:id="apiKeysTab" text="API Keys">
            <content>
              <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                     <children>
                        <Label fx:id="deeplKeyLabel" layoutX="30.0" layoutY="30.0" text="DeepL API Key:">
                           <font>
                              <Font size="14.0" />
                           </font>
                        </Label>
                        <PasswordField fx:id="deeplApiKeyField" layoutX="30.0" layoutY="55.0" onMouseClicked="#handleDeeplKeyFieldFocus" prefHeight="30.0" prefWidth="400.0" promptText="Enter your DeepL API key" />
                        <Label fx:id="deeplApiStatusIndicator" layoutX="440.0" layoutY="60.0" prefHeight="20.0" prefWidth="30.0" text="⟳" textFill="#FFA500" alignment="CENTER">
                           <font>
                              <Font size="16.0" />
                           </font>
                        </Label>
                        <Label fx:id="deeplInfoLabel" layoutX="30.0" layoutY="90.0" prefHeight="20.0" prefWidth="520.0" text="Get your free API key from https://www.deepl.com/pro-api" textFill="#666666">
                           <font>
                              <Font size="11.0" />
                           </font>
                        </Label>
                        <Label fx:id="googleKeyLabel" layoutX="30.0" layoutY="130.0" text="Google Translate API Key:">
                           <font>
                              <Font size="14.0" />
                           </font>
                        </Label>
                        <PasswordField fx:id="googleApiKeyField" layoutX="30.0" layoutY="155.0" onMouseClicked="#handleGoogleKeyFieldFocus" prefHeight="30.0" prefWidth="400.0" promptText="Enter your Google Translate API key" />
                        <Label fx:id="googleApiStatusIndicator" layoutX="440.0" layoutY="160.0" prefHeight="20.0" prefWidth="30.0" text="⟳" textFill="#FFA500" alignment="CENTER">
                           <font>
                              <Font size="16.0" />
                           </font>
                        </Label>
                        <Label fx:id="googleInfoLabel" layoutX="30.0" layoutY="190.0" prefHeight="20.0" prefWidth="520.0" text="Get your API key from Google Cloud Console" textFill="#666666">
                           <font>
                              <Font size="11.0" />
                           </font>
                        </Label>
                        <Label fx:id="securityNoteLabel" layoutX="30.0" layoutY="230.0" prefHeight="40.0" prefWidth="520.0" text="Security Note: API keys are stored locally on your computer and are not transmitted to any third parties except the respective translation services." textFill="#999999" wrapText="true">
                           <font>
                              <Font size="10.0" />
                           </font>
                        </Label>
                     </children>
                  </AnchorPane>
            </content>
          </Tab>
        </tabs>
      </TabPane>
      <AnchorPane prefHeight="60.0" prefWidth="600.0" style="-fx-background-color: #f4f4f4; -fx-border-color: #d0d0d0; -fx-border-width: 1 0 0 0;" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0">
         <children>
            <HBox alignment="CENTER_RIGHT" spacing="10.0" AnchorPane.bottomAnchor="15.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="15.0">
               <children>
                  <Button fx:id="cancelButton" mnemonicParsing="false" onAction="#handleCancel" prefHeight="30.0" prefWidth="80.0" text="Cancel" />
                  <Button fx:id="saveButton" mnemonicParsing="false" onAction="#handleSave" prefHeight="30.0" prefWidth="80.0" text="Save" />
               </children>
            </HBox>
         </children>
      </AnchorPane>
   </children>
</AnchorPane>
