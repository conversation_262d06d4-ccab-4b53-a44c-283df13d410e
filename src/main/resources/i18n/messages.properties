# English Messages for Excel Translator

# Application
app.title=Excel Translator - PLAN-B
app.ready=Ready
app.loading=Loading...
app.processing=Processing...

# Window Titles
window.main.title=Excel Translator
window.preferences.title=Preferences
window.about.title=About Excel Translator

# Menu Items
menu.file=File
menu.edit=Edit
menu.help=Help
menu.file.exit=Exit
menu.edit.preferences=Preferences...
menu.help.about=About Excel Translator

# File Operations
file.dragdrop=Drag and drop your Excel file here
file.browse=Browse File
file.browse.helper=or click Browse to select
file.select=Select File
file.no_file_selected=No file selected
file.drag_drop_hint=Drag and drop Excel file here or click to select
file.invalid_format=Invalid file format. Please select a .xlsx or .xls file.
file.too_large=File size exceeds maximum allowed size ({0} MB).
file.not_found=File not found
file.read_error=Failed to read Excel file
file.export_success=File exported successfully
file.export_error=Failed to export file

# Translation
translation.start=Start Translation
translation.cancel=Cancel
translation.in_progress=Translating...
translation.completed=Translation completed
translation.failed=Translation failed
translation.cancelled=Translation cancelled
translation.no_sheets_selected=Please select at least one sheet to translate
translation.invalid_language_pair=Invalid language combination
translation.service_unavailable=Translation service is not available

# UI Labels
logs.title=Activity Logs
language.source=Source Language
language.target=Target Language
translator.title=Translation Service
batchsize.title=Batch Size (cells per request)
sheets.detected=Detected Worksheets
status.label=Status

# Languages
language.autodetect=Auto-Detect
language.select.target=Select target language...
language.english=English
language.japanese=Japanese
language.vietnamese=Vietnamese

# Translators
translator.deepl.recommended=DeepL Translator (Recommended)
translator.google=Google Translator

# Buttons
button.translate=Start Translation
button.cancel=Reset
button.export=Export Translated File
button.save=Save
button.cancel.preferences=Cancel

# Status
status.ready=Ready to translate
status.translating=Translation in progress...
status.completed=Translation completed successfully
status.file.loaded=File loaded - Ready to translate
progress.translating=Translating...

# Preferences
preferences.title=Application Preferences
preferences.tab.interface=Interface Settings
preferences.tab.apikeys=API Configuration
preferences.interface.language=Interface Language:
preferences.interface.note=Note: Interface language changes are applied immediately
preferences.saved.title=Settings Saved
preferences.saved.message=Your preferences have been saved successfully.

# UI Elements (legacy)
ui.source_language=Source Language
ui.target_language=Target Language
ui.translator=Translator
ui.batch_size=Batch Size
ui.sheets_detected=Sheets Detected
ui.logs=Logs
ui.progress=Progress
ui.export=Export
ui.translate=Translate

# Errors
error.general=An error occurred
error.network=Network connection error
error.api_key_invalid=Invalid API key
error.api_key_missing=Missing API key
error.rate_limit_exceeded=Rate limit exceeded
error.quota_exceeded=Translation quota exceeded
error.service_unavailable=Service temporarily unavailable
error.unsupported_language=Unsupported language combination

# Error Dialog Titles
error.title.initialization=Initialization Error
error.title.file=File Error
error.title.invalid_selection=Invalid Selection
error.title.no_file=No File Selected
error.title.no_sheets=No Sheets Selected
error.title.no_result=No Translation Result
error.title.invalid_file_type=Invalid File Type
error.title.analysis=Analysis Error
error.title.translation_failed=Translation Failed
error.title.export_failed=Export Failed
error.title.preferences=Preferences Error

# Error Messages
error.message.initialization_failed=Failed to initialize the application: {0}
error.message.no_file_selected=Please select an Excel file first.
error.message.select_target_language=Please select a target language.
error.message.select_translator=Please select a translator.
error.message.select_sheets=Please select at least one sheet to translate.
error.message.no_translation_result=Please complete a translation first.
error.message.file_not_exist=Selected file does not exist.
error.message.invalid_file_type=Please select a valid Excel file (.xlsx or .xls).
error.message.analysis_failed=Failed to analyze Excel file: {0}
error.message.file_read_failed=Failed to read file information: {0}
error.message.translation_failed=Translation failed: {0}
error.message.export_failed=Failed to export file: {0}
error.message.preferences_failed=Failed to open preferences window: {0}

# Success Messages
success.translation_complete=Translation completed successfully
success.file_exported=File exported successfully
success.backup_created=Backup file created

# Information Dialog Titles
info.title.translation_complete=Translation Complete
info.title.file_exported=File Exported

# Warning Dialog Titles
warning.title.translation_warnings=Translation Warnings

# Warning Messages
warning.no_backup=Could not create backup file
warning.partial_translation=Some cells could not be translated
warning.service_degraded=Translation service is running with reduced performance

# Complex Error Messages
error.message.translation_failed_multiple=Translation failed with {0} errors
error.message.translation_warnings_multiple=Translation completed with {0} warnings

# API Keys
preferences.api.tab=API Keys
preferences.api.deepl.label=DeepL API Key:
preferences.api.deepl.placeholder=Enter your DeepL API key
preferences.api.deepl.info=Get your free API key from https://www.deepl.com/pro-api
preferences.api.google.label=Google Translate API Key:
preferences.api.google.placeholder=Enter your Google Translate API key
preferences.api.google.info=Get your API key from Google Cloud Console
preferences.api.security.note=Security Note: API keys are stored locally on your computer and are not transmitted to any third parties except the respective translation services.
preferences.api.key.set=API key is set (click to change)
