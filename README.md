# Excel Translator - PLAN-B

A JavaFX application for translating Excel files using DeepL and Google Translate APIs.

## Features

- **Multi-format Support**: Supports both .xlsx and .xls Excel files
- **Multiple Translation Services**: DeepL Translator (default) and Google Translator
- **Language Support**: Auto-detect source language, translate between Vietnamese, Japanese, and English
- **Batch Processing**: Intelligent batching to optimize API usage and reduce latency
- **Sheet Detection**: Automatically detects and processes multiple sheets
- **Text Preservation**: Preserves text in square brackets [] and Japanese quotation marks 「」
- **Progress Tracking**: Real-time progress bar and detailed logging
- **Drag & Drop**: Easy file selection with drag-and-drop support
- **Export Functionality**: Download translated files with a single click
- **Performance Optimized**: Handles large files (10,000+ rows) efficiently
- **Background Processing**: Non-blocking UI with background translation tasks
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Multi-language UI**: Supports Japanese, Vietnamese, and English interfaces

## Requirements

- Java 17 or higher
- Maven 3.6 or higher
- API keys for DeepL and/or Google Translate

## Setup

### 1. <PERSON>lone the Repository

```bash
git clone <repository-url>
cd Excel
```

### 2. Configure API Keys

You can configure API keys in several ways:

#### Option A: Environment Variables (Recommended)
```bash
export DEEPL_API_KEY="your-deepl-api-key"
export GOOGLE_API_KEY="your-google-api-key"
```

#### Option B: System Properties
```bash
-Ddeepl.api.key="your-deepl-api-key"
-Dgoogle.api.key="your-google-api-key"
```

#### Option C: Configuration File
Edit `src/main/resources/application.conf`:
```hocon
deepl {
  api {
    key = "your-deepl-api-key"
  }
}

google {
  api {
    key = "your-google-api-key"
  }
}
```

### 3. Build the Project

```bash
mvn clean compile
```

### 4. Run the Application

**Option 1: Using the provided script (Recommended)**
```bash
./run.sh
```

**Option 2: Using Maven directly**
```bash
mvn javafx:run
```

**Note**: The JAR file approach has JavaFX module issues on some systems. Use Maven for the most reliable execution.

## Usage

1. **Select File**: Click "Select File" or drag and drop an Excel file (.xlsx or .xls)
2. **Configure Translation**:
   - Choose source language (Auto-Detect recommended)
   - Select target language
   - Choose translator (DeepL or Google)
   - Adjust batch size if needed (default: 50)
3. **Select Sheets**: Choose which sheets to translate from the detected list
4. **Translate**: Click "Translate" to start the process
5. **Monitor Progress**: Watch the progress bar and logs for real-time updates
6. **Export**: Click "Export" to save the translated file

## Configuration

### Application Settings

The application can be configured through `application.conf`:

```hocon
# Translation settings
translation {
  batch.size = 50              # Default batch size
  batch.max.size = 100         # Maximum batch size
  request.timeout.seconds = 30 # API request timeout
  rate.limit = 5               # Requests per second
}

# File settings
file {
  max.size.mb = 50            # Maximum file size
  create.backup = true        # Create backup files
}

# UI settings
ui {
  language = "en"             # UI language (en, ja, vi)
}
```

### Performance Tuning

For optimal performance with large files:

- **Batch Size**: Increase for fewer API calls, decrease for better progress tracking
- **Rate Limit**: Adjust based on your API plan (free plans typically allow 5 req/sec)
- **Memory**: Use `-Xmx2g` for large files

Example for large files:
```bash
java -Xmx2g -Dtranslation.batch.size=100 -jar excel-translator-1.0.0-shaded.jar
```

## API Rate Limits

### DeepL API (Free Plan)
- 500,000 characters/month
- 5 requests/second recommended

### Google Translate API
- Pay-per-use pricing
- Higher rate limits available

### Batch Size Calculation
```
Batch size: 50
Maximum requests in 60 seconds: 5 × 60 = 300 requests
Maximum rows: 300 × 50 = 15,000 rows
```

## Architecture

The application follows hexagonal architecture principles:

- **Domain Layer**: Core business logic and models
- **Application Layer**: Use cases and orchestration
- **Infrastructure Layer**: External services (APIs, file I/O)
- **UI Layer**: JavaFX controllers and views

### Key Components

- `TranslationApplicationService`: Main orchestration service
- `TranslationServiceFactory`: Creates translation service instances
- `DeepLTranslationService`: DeepL API implementation
- `GoogleTranslationService`: Google Translate API implementation
- `ApachePOIExcelProcessor`: Excel file processing
- `DashboardController`: JavaFX UI controller

## Testing

Run unit tests:
```bash
mvn test
```

Run with coverage:
```bash
mvn test jacoco:report
```

## Troubleshooting

### Common Issues

1. **"No API key configured"**
   - Ensure API keys are set via environment variables or configuration file
   - Check that the keys are valid and not expired

2. **"Translation service unavailable"**
   - Check internet connection
   - Verify API key permissions
   - Check API service status

3. **"File format not supported"**
   - Ensure file has .xlsx or .xls extension
   - Try opening the file in Excel to verify it's not corrupted

4. **"Rate limit exceeded"**
   - Reduce batch size
   - Wait before retrying
   - Consider upgrading API plan

5. **Memory issues with large files**
   - Increase JVM heap size: `-Xmx4g`
   - Reduce batch size
   - Process sheets individually

### Logging

Logs are written to:
- Console (during development)
- `logs/excel-translator.log` (production)

Enable debug logging:
```bash
-Dlogging.level=DEBUG
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the troubleshooting section
- Review the logs for error details
- Create an issue in the repository
