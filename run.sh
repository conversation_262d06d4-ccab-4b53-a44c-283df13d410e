#!/bin/bash

# Excel Translator Runner Script
# This script sets up the environment and runs the Excel Translator application

echo "Excel Translator - PLAN-B"
echo "=========================="

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    echo "Please install Java 17 or higher"
    exit 1
fi

# Check Java version
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo "Error: Java 17 or higher is required"
    echo "Current Java version: $JAVA_VERSION"
    exit 1
fi

# Check if <PERSON><PERSON> is installed
if ! command -v mvn &> /dev/null; then
    echo "Error: Maven is not installed or not in PATH"
    echo "Please install Maven 3.6 or higher"
    exit 1
fi

# Set API keys if not already set
if [ -z "$DEEPL_API_KEY" ] && [ -z "$GOOGLE_API_KEY" ]; then
    echo "Warning: No API keys found in environment variables"
    echo "The application will use keys from application.conf if available"
    echo ""
    echo "To set API keys, use:"
    echo "export DEEPL_API_KEY=\"your-deepl-key\""
    echo "export GOOGLE_API_KEY=\"your-google-key\""
    echo ""
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Build the project if needed
echo "Building the project..."
mvn clean compile -q

if [ $? -ne 0 ]; then
    echo "Error: Failed to build the project"
    exit 1
fi
echo "Build completed successfully"

echo "Starting Excel Translator..."
echo ""

# Run the application using Maven JavaFX plugin (handles JavaFX dependencies properly)
mvn javafx:run

echo ""
echo "Excel Translator has been closed"
