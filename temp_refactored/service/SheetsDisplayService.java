package com.planb.exceltranslator.ui.service;

import com.planb.exceltranslator.domain.model.ExcelSheet;
import javafx.geometry.Insets;
import javafx.scene.control.CheckBox;
import javafx.scene.control.Label;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.VBox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Service responsible for displaying and managing Excel sheets in the UI.
 * Handles sheet selection, display formatting, and sheet information presentation.
 */
public class SheetsDisplayService {
    
    private static final Logger logger = LoggerFactory.getLogger(SheetsDisplayService.class);
    
    private List<SheetDisplayItem> displayItems = new ArrayList<>();
    
    public SheetsDisplayService() {
        logger.debug("SheetsDisplayService created");
    }
    
    /**
     * Displays the list of Excel sheets in the specified container
     * 
     * @param container The container to display sheets in
     * @param sheets The list of sheets to display
     */
    public void displaySheets(AnchorPane container, List<ExcelSheet> sheets) {
        if (container == null || sheets == null) {
            logger.warn("Cannot display sheets - container or sheets is null");
            return;
        }
        
        logger.debug("Displaying {} sheets", sheets.size());
        
        // Clear existing content
        container.getChildren().clear();
        displayItems.clear();
        
        // Create VBox for sheet list
        VBox sheetsList = new VBox(8);
        sheetsList.setPadding(new Insets(10));
        
        // Add each sheet as a display item
        for (ExcelSheet sheet : sheets) {
            SheetDisplayItem item = createSheetDisplayItem(sheet);
            displayItems.add(item);
            sheetsList.getChildren().add(item.getDisplayNode());
        }
        
        // Anchor the VBox to fill the container
        AnchorPane.setTopAnchor(sheetsList, 0.0);
        AnchorPane.setBottomAnchor(sheetsList, 0.0);
        AnchorPane.setLeftAnchor(sheetsList, 0.0);
        AnchorPane.setRightAnchor(sheetsList, 0.0);
        
        container.getChildren().add(sheetsList);
        
        logger.info("Displayed {} sheets successfully", sheets.size());
    }
    
    /**
     * Gets the list of currently selected sheets
     * 
     * @return List of selected sheets
     */
    public List<ExcelSheet> getSelectedSheets() {
        List<ExcelSheet> selectedSheets = new ArrayList<>();
        
        for (SheetDisplayItem item : displayItems) {
            if (item.isSelected()) {
                selectedSheets.add(item.getSheet());
            }
        }
        
        logger.debug("Found {} selected sheets", selectedSheets.size());
        return selectedSheets;
    }
    
    /**
     * Selects or deselects all sheets
     * 
     * @param selected true to select all, false to deselect all
     */
    public void selectAllSheets(boolean selected) {
        for (SheetDisplayItem item : displayItems) {
            item.setSelected(selected);
        }
        
        logger.debug("Set all sheets selection to: {}", selected);
    }
    
    /**
     * Gets the total number of translatable cells across selected sheets
     * 
     * @return Total number of translatable cells
     */
    public int getTotalTranslatableCells() {
        return getSelectedSheets().stream()
            .mapToInt(ExcelSheet::getTranslatableCellCount)
            .sum();
    }
    
    /**
     * Gets the total number of sheets being displayed
     * 
     * @return Total number of sheets
     */
    public int getTotalSheetCount() {
        return displayItems.size();
    }
    
    /**
     * Gets the number of currently selected sheets
     * 
     * @return Number of selected sheets
     */
    public int getSelectedSheetCount() {
        return getSelectedSheets().size();
    }
    
    /**
     * Checks if any sheets are selected
     * 
     * @return true if at least one sheet is selected, false otherwise
     */
    public boolean hasSelectedSheets() {
        return getSelectedSheetCount() > 0;
    }
    
    /**
     * Sets a callback for when sheet selection changes
     * 
     * @param callback Callback to be invoked when selection changes
     */
    public void setSelectionChangeCallback(Runnable callback) {
        for (SheetDisplayItem item : displayItems) {
            item.setSelectionChangeCallback(callback);
        }
    }
    
    /**
     * Creates a display item for a single sheet
     */
    private SheetDisplayItem createSheetDisplayItem(ExcelSheet sheet) {
        return new SheetDisplayItem(sheet);
    }
    
    /**
     * Clears all displayed sheets
     */
    public void clear() {
        displayItems.clear();
        logger.debug("Cleared all sheet display items");
    }
    
    /**
     * Inner class representing a single sheet display item
     */
    private static class SheetDisplayItem {
        private final ExcelSheet sheet;
        private final CheckBox checkBox;
        private final VBox displayNode;
        private Runnable selectionChangeCallback;
        
        public SheetDisplayItem(ExcelSheet sheet) {
            this.sheet = sheet;
            this.checkBox = new CheckBox();
            this.displayNode = createDisplayNode();
        }
        
        private VBox createDisplayNode() {
            VBox container = new VBox(4);
            container.setPadding(new Insets(5));
            container.setStyle("-fx-border-color: #e0e0e0; -fx-border-width: 1px; -fx-background-color: #fafafa;");
            
            // Main checkbox with sheet name
            checkBox.setText(sheet.getName());
            checkBox.setSelected(true); // Default to selected
            checkBox.setOnAction(event -> {
                if (selectionChangeCallback != null) {
                    selectionChangeCallback.run();
                }
            });
            
            // Sheet information
            Label infoLabel = new Label(formatSheetInfo());
            infoLabel.setStyle("-fx-text-fill: #666666; -fx-font-size: 11px;");
            
            container.getChildren().addAll(checkBox, infoLabel);
            return container;
        }
        
        private String formatSheetInfo() {
            return String.format("Rows: %d, Translatable cells: %d", 
                sheet.getRowCount(), 
                sheet.getTranslatableCellCount());
        }
        
        public ExcelSheet getSheet() {
            return sheet;
        }
        
        public boolean isSelected() {
            return checkBox.isSelected();
        }
        
        public void setSelected(boolean selected) {
            checkBox.setSelected(selected);
        }
        
        public VBox getDisplayNode() {
            return displayNode;
        }
        
        public void setSelectionChangeCallback(Runnable callback) {
            this.selectionChangeCallback = callback;
        }
    }
}
