package com.planb.exceltranslator.ui.service;

import com.planb.exceltranslator.application.TranslationApplicationService;
import com.planb.exceltranslator.application.TranslationWorkflowService;
import com.planb.exceltranslator.domain.model.TranslationRequest;
import com.planb.exceltranslator.domain.model.TranslationResult;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import javafx.concurrent.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * Service responsible for controlling translation operations.
 * Manages translation workflow, progress tracking, and cancellation.
 */
public class TranslationControlService {
    
    private static final Logger logger = LoggerFactory.getLogger(TranslationControlService.class);
    
    private final ConfigurationManager configManager;
    private final TranslationApplicationService translationService;
    private final TranslationWorkflowService workflowService;
    
    private TranslationWorkflowService.TranslationTask currentTask;
    private boolean translationInProgress = false;
    
    public TranslationControlService(ConfigurationManager configManager) {
        this.configManager = configManager;
        this.translationService = new TranslationApplicationService(configManager);
        this.workflowService = new TranslationWorkflowService(configManager, translationService);
        logger.debug("TranslationControlService created");
    }
    
    /**
     * Starts a translation operation asynchronously
     * 
     * @param request The translation request containing all parameters
     * @param progressCallback Callback for progress updates
     * @param logCallback Callback for log messages
     * @return CompletableFuture with translation result
     */
    public CompletableFuture<TranslationResult> startTranslationAsync(
            TranslationRequest request,
            Consumer<TranslationWorkflowService.WorkflowProgress> progressCallback,
            Consumer<String> logCallback) {
        
        if (translationInProgress) {
            logger.warn("Translation already in progress, cannot start new translation");
            return CompletableFuture.failedFuture(
                new IllegalStateException("Translation already in progress"));
        }
        
        logger.info("Starting translation for file: {}", request.getFile().getName());
        translationInProgress = true;
        
        // Set up callbacks
        workflowService.setProgressCallback(progressCallback);
        workflowService.setLogCallback(logCallback);
        
        // Start translation workflow
        currentTask = workflowService.startTranslationAsync(request);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Wait for translation to complete
                while (currentTask.isRunning()) {
                    Thread.sleep(100);
                }
                
                if (currentTask.isCancelled()) {
                    throw new RuntimeException("Translation was cancelled");
                }
                
                TranslationResult result = currentTask.getResult();
                logger.info("Translation completed successfully");
                return result;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Translation was interrupted", e);
            } catch (Exception e) {
                logger.error("Translation failed", e);
                throw new RuntimeException("Translation failed: " + e.getMessage(), e);
            } finally {
                translationInProgress = false;
                currentTask = null;
            }
        });
    }
    
    /**
     * Cancels the current translation operation
     * 
     * @return true if cancellation was successful, false if no translation in progress
     */
    public boolean cancelTranslation() {
        if (!translationInProgress || currentTask == null) {
            logger.warn("No translation in progress to cancel");
            return false;
        }
        
        logger.info("Cancelling translation...");
        boolean cancelled = currentTask.cancel();
        
        if (cancelled) {
            translationInProgress = false;
            currentTask = null;
            logger.info("Translation cancelled successfully");
        } else {
            logger.warn("Failed to cancel translation");
        }
        
        return cancelled;
    }
    
    /**
     * Exports a translated file asynchronously
     * 
     * @param translationResult The translation result to export
     * @param outputFile The file to save the translated content to
     * @return CompletableFuture for the export operation
     */
    public CompletableFuture<Void> exportTranslatedFileAsync(TranslationResult translationResult, File outputFile) {
        logger.info("Starting export to file: {}", outputFile.getAbsolutePath());
        
        return CompletableFuture.runAsync(() -> {
            try {
                translationService.exportTranslatedFile(translationResult, outputFile);
                logger.info("File exported successfully to: {}", outputFile.getAbsolutePath());
                
            } catch (Exception e) {
                logger.error("Failed to export file", e);
                throw new RuntimeException("Export failed: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * Gets the current translation progress
     * 
     * @return Current progress information or null if no translation in progress
     */
    public TranslationWorkflowService.WorkflowProgress getCurrentProgress() {
        if (currentTask != null && translationInProgress) {
            // This would need to be implemented in the workflow service
            // For now, return null
            return null;
        }
        return null;
    }
    
    /**
     * Checks if a translation is currently in progress
     * 
     * @return true if translation is in progress, false otherwise
     */
    public boolean isTranslationInProgress() {
        return translationInProgress;
    }
    
    /**
     * Gets the current translation task
     * 
     * @return Current translation task or null if none
     */
    public TranslationWorkflowService.TranslationTask getCurrentTask() {
        return currentTask;
    }
    
    /**
     * Validates translation parameters before starting translation
     * 
     * @param request The translation request to validate
     * @return Validation result with error message if invalid
     */
    public ValidationResult validateTranslationRequest(TranslationRequest request) {
        if (request == null) {
            return ValidationResult.error("Translation request cannot be null");
        }
        
        if (request.getFile() == null || !request.getFile().exists()) {
            return ValidationResult.error("Source file does not exist");
        }
        
        if (request.getSheets() == null || request.getSheets().isEmpty()) {
            return ValidationResult.error("No sheets selected for translation");
        }
        
        if (request.getSourceLanguage() == null) {
            return ValidationResult.error("Source language must be specified");
        }
        
        if (request.getTargetLanguage() == null) {
            return ValidationResult.error("Target language must be specified");
        }
        
        if (request.getTranslatorType() == null) {
            return ValidationResult.error("Translator type must be specified");
        }
        
        if (request.getBatchSize() <= 0) {
            return ValidationResult.error("Batch size must be greater than zero");
        }
        
        int maxBatchSize = configManager.getMaxBatchSize();
        if (request.getBatchSize() > maxBatchSize) {
            return ValidationResult.error("Batch size cannot exceed " + maxBatchSize);
        }
        
        return ValidationResult.success();
    }
    
    /**
     * Estimates the time required for translation
     * 
     * @param request The translation request
     * @return Estimated time in seconds
     */
    public int estimateTranslationTime(TranslationRequest request) {
        try {
            return translationService.estimateProcessingTime(request.getFile());
        } catch (Exception e) {
            logger.warn("Failed to estimate translation time", e);
            return 60; // Default fallback
        }
    }
    
    /**
     * Cleanup resources when service is no longer needed
     */
    public void cleanup() {
        if (translationInProgress) {
            cancelTranslation();
        }
        
        if (workflowService != null) {
            workflowService.cleanup();
        }
        
        logger.debug("TranslationControlService cleanup completed");
    }
    
    /**
     * Result class for validation operations
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        
        private ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public static ValidationResult success() {
            return new ValidationResult(true, null);
        }
        
        public static ValidationResult error(String message) {
            return new ValidationResult(false, message);
        }
    }
}
