package com.planb.exceltranslator.ui.service;

import com.planb.exceltranslator.application.LanguageDetectionService;
import com.planb.exceltranslator.domain.model.ExcelSheet;
import com.planb.exceltranslator.domain.model.Language;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.scene.control.ComboBox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;

/**
 * Service responsible for managing language bindings and language detection.
 * Handles language combo box interactions, auto-detection, and language suggestions.
 */
public class LanguageBindingService {
    
    private static final Logger logger = LoggerFactory.getLogger(LanguageBindingService.class);
    
    private final LanguageManager languageManager;
    private final LanguageDetectionService languageDetectionService;
    
    public LanguageBindingService(LanguageManager languageManager) {
        this.languageManager = languageManager;
        this.languageDetectionService = new LanguageDetectionService();
        logger.debug("LanguageBindingService created");
    }
    
    /**
     * Sets up language bindings for source and target language combo boxes
     * 
     * @param sourceLanguage Source language combo box
     * @param targetLanguage Target language combo box
     * @param changeCallback Callback invoked when languages change
     */
    public void setupLanguageBindings(ComboBox<Language> sourceLanguage,
                                    ComboBox<Language> targetLanguage,
                                    BiConsumer<Language, Language> changeCallback) {
        
        logger.debug("Setting up language bindings");
        
        // Set up source language change listener
        sourceLanguage.valueProperty().addListener((observable, oldValue, newValue) -> {
            if (oldValue != newValue) {
                handleSourceLanguageChange(newValue, targetLanguage, changeCallback);
            }
        });
        
        // Set up target language change listener
        targetLanguage.valueProperty().addListener((observable, oldValue, newValue) -> {
            if (oldValue != newValue) {
                handleTargetLanguageChange(sourceLanguage.getValue(), newValue, changeCallback);
            }
        });
        
        logger.debug("Language bindings set up successfully");
    }
    
    /**
     * Detects the language of the given sheets asynchronously
     * 
     * @param sheets List of Excel sheets to analyze
     * @param currentSourceLanguage Currently selected source language
     * @return CompletableFuture with detected language or null if detection fails
     */
    public CompletableFuture<Language> detectLanguageAsync(List<ExcelSheet> sheets, Language currentSourceLanguage) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.debug("Starting language detection for {} sheets", sheets.size());
                
                // Skip detection if not set to auto-detect
                if (currentSourceLanguage != Language.AUTO_DETECT) {
                    logger.debug("Auto-detection disabled, current language: {}", currentSourceLanguage);
                    return null;
                }
                
                // Extract sample text from sheets for detection
                String sampleText = extractSampleText(sheets);
                if (sampleText.trim().isEmpty()) {
                    logger.warn("No text found for language detection");
                    return null;
                }
                
                // Perform language detection
                LanguageDetectionService.LanguageDetectionResult result = 
                    languageDetectionService.detectLanguage(sampleText);
                
                if (result.getStatus() == LanguageDetectionService.LanguageDetectionResult.DetectionStatus.SUCCESS) {
                    Language detectedLanguage = result.getDetectedLanguage();
                    logger.info("Language detected: {}", detectedLanguage);
                    return detectedLanguage;
                } else {
                    logger.warn("Language detection failed: {}", result.getStatus());
                    return null;
                }
                
            } catch (Exception e) {
                logger.error("Error during language detection", e);
                return null;
            }
        });
    }
    
    /**
     * Suggests an alternative target language when source and target are the same
     * 
     * @param sourceLanguage The source language
     * @return Suggested alternative target language or null if no suggestion
     */
    public Language suggestAlternativeTarget(Language sourceLanguage) {
        if (sourceLanguage == null) {
            return null;
        }
        
        // Common alternative suggestions
        switch (sourceLanguage) {
            case ENGLISH:
                return Language.SPANISH;
            case SPANISH:
                return Language.ENGLISH;
            case FRENCH:
                return Language.ENGLISH;
            case GERMAN:
                return Language.ENGLISH;
            case JAPANESE:
                return Language.ENGLISH;
            case CHINESE_SIMPLIFIED:
                return Language.ENGLISH;
            case CHINESE_TRADITIONAL:
                return Language.ENGLISH;
            default:
                return Language.ENGLISH; // Default fallback
        }
    }
    
    /**
     * Validates language selection
     * 
     * @param sourceLanguage Selected source language
     * @param targetLanguage Selected target language
     * @return Validation result
     */
    public LanguageValidationResult validateLanguageSelection(Language sourceLanguage, Language targetLanguage) {
        if (sourceLanguage == null) {
            return LanguageValidationResult.error("Source language must be selected");
        }
        
        if (targetLanguage == null) {
            return LanguageValidationResult.error("Target language must be selected");
        }
        
        if (sourceLanguage == Language.AUTO_DETECT) {
            return LanguageValidationResult.warning("Auto-detect selected - language will be detected automatically");
        }
        
        if (sourceLanguage.equals(targetLanguage)) {
            return LanguageValidationResult.warning("Source and target languages are the same");
        }
        
        return LanguageValidationResult.success();
    }
    
    /**
     * Gets supported language pairs for a specific translator
     * 
     * @param translatorType The translator type to check
     * @return List of supported source languages
     */
    public List<Language> getSupportedSourceLanguages(String translatorType) {
        // This would typically check with the specific translator's capabilities
        // For now, return all available languages
        return List.of(Language.values());
    }
    
    /**
     * Gets supported target languages for a given source language and translator
     * 
     * @param sourceLanguage The source language
     * @param translatorType The translator type
     * @return List of supported target languages
     */
    public List<Language> getSupportedTargetLanguages(Language sourceLanguage, String translatorType) {
        // This would typically check with the specific translator's capabilities
        // For now, return all available languages except the source
        return List.of(Language.values());
    }
    
    // Private helper methods
    
    private void handleSourceLanguageChange(Language newSourceLanguage, 
                                          ComboBox<Language> targetLanguage,
                                          BiConsumer<Language, Language> changeCallback) {
        logger.debug("Source language changed to: {}", newSourceLanguage);
        
        Language currentTarget = targetLanguage.getValue();
        
        // Check if we need to suggest an alternative target
        if (newSourceLanguage != null && newSourceLanguage.equals(currentTarget)) {
            Language suggested = suggestAlternativeTarget(newSourceLanguage);
            if (suggested != null) {
                targetLanguage.setValue(suggested);
                logger.debug("Auto-suggested target language: {}", suggested);
                currentTarget = suggested;
            }
        }
        
        if (changeCallback != null) {
            changeCallback.accept(newSourceLanguage, currentTarget);
        }
    }
    
    private void handleTargetLanguageChange(Language sourceLanguage, 
                                          Language newTargetLanguage,
                                          BiConsumer<Language, Language> changeCallback) {
        logger.debug("Target language changed to: {}", newTargetLanguage);
        
        if (changeCallback != null) {
            changeCallback.accept(sourceLanguage, newTargetLanguage);
        }
    }
    
    /**
     * Extracts sample text from Excel sheets for language detection
     */
    private String extractSampleText(List<ExcelSheet> sheets) {
        StringBuilder sampleText = new StringBuilder();
        int sampleCount = 0;
        final int MAX_SAMPLES = 50; // Limit to avoid processing too much text
        
        for (ExcelSheet sheet : sheets) {
            // This would need to be implemented based on the actual ExcelSheet structure
            // For now, we'll assume a method to get sample text exists
            // sampleText.append(sheet.getSampleText()).append(" ");
            // sampleCount++;
            
            if (sampleCount >= MAX_SAMPLES) {
                break;
            }
        }
        
        return sampleText.toString();
    }
    
    /**
     * Result class for language validation
     */
    public static class LanguageValidationResult {
        private final boolean isValid;
        private final boolean isWarning;
        private final String message;
        
        private LanguageValidationResult(boolean isValid, boolean isWarning, String message) {
            this.isValid = isValid;
            this.isWarning = isWarning;
            this.message = message;
        }
        
        public boolean isValid() {
            return isValid;
        }
        
        public boolean isWarning() {
            return isWarning;
        }
        
        public String getMessage() {
            return message;
        }
        
        public static LanguageValidationResult success() {
            return new LanguageValidationResult(true, false, null);
        }
        
        public static LanguageValidationResult warning(String message) {
            return new LanguageValidationResult(true, true, message);
        }
        
        public static LanguageValidationResult error(String message) {
            return new LanguageValidationResult(false, false, message);
        }
    }
}
