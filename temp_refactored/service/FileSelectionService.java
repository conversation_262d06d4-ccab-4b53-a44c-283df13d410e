package com.planb.exceltranslator.ui.service;

import com.planb.exceltranslator.application.FileAnalysisService;
import com.planb.exceltranslator.application.TranslationApplicationService;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import javafx.stage.FileChooser;
import javafx.stage.Window;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.concurrent.CompletableFuture;

/**
 * Service responsible for handling file selection and file analysis operations.
 * Provides file chooser dialogs and manages file validation and analysis.
 */
public class FileSelectionService {
    
    private static final Logger logger = LoggerFactory.getLogger(FileSelectionService.class);
    
    private final ConfigurationManager configManager;
    private final FileAnalysisService fileAnalysisService;
    
    public FileSelectionService(ConfigurationManager configManager) {
        this.configManager = configManager;
        this.fileAnalysisService = new FileAnalysisService(configManager, 
            new TranslationApplicationService(configManager));
        logger.debug("FileSelectionService created");
    }
    
    /**
     * Shows a file chooser dialog for selecting Excel files
     * 
     * @return Selected file or null if cancelled
     */
    public File selectExcelFile() {
        return selectExcelFile(null);
    }
    
    /**
     * Shows a file chooser dialog for selecting Excel files
     * 
     * @param owner Owner window for the dialog
     * @return Selected file or null if cancelled
     */
    public File selectExcelFile(Window owner) {
        logger.debug("Opening Excel file selection dialog");
        
        FileChooser fileChooser = createExcelFileChooser();
        fileChooser.setTitle("Select Excel File to Translate");
        
        File selectedFile = fileChooser.showOpenDialog(owner);
        
        if (selectedFile != null) {
            logger.info("Excel file selected: {}", selectedFile.getAbsolutePath());
            return selectedFile;
        } else {
            logger.debug("File selection cancelled");
            return null;
        }
    }
    
    /**
     * Shows a file chooser dialog for selecting output file location
     * 
     * @param originalFile The original input file for generating default name
     * @return Selected output file or null if cancelled
     */
    public File selectOutputFile(File originalFile) {
        return selectOutputFile(originalFile, null);
    }
    
    /**
     * Shows a file chooser dialog for selecting output file location
     * 
     * @param originalFile The original input file for generating default name
     * @param owner Owner window for the dialog
     * @return Selected output file or null if cancelled
     */
    public File selectOutputFile(File originalFile, Window owner) {
        logger.debug("Opening output file selection dialog");
        
        FileChooser fileChooser = createExcelFileChooser();
        fileChooser.setTitle("Save Translated File");
        
        if (originalFile != null) {
            String originalName = originalFile.getName();
            String extension = getFileExtension(originalName);
            String baseName = originalName.substring(0, originalName.lastIndexOf('.'));
            String defaultName = baseName + "_translated" + extension;
            
            fileChooser.setInitialFileName(defaultName);
            fileChooser.setInitialDirectory(originalFile.getParentFile());
        }
        
        File outputFile = fileChooser.showSaveDialog(owner);
        
        if (outputFile != null) {
            logger.info("Output file selected: {}", outputFile.getAbsolutePath());
            return outputFile;
        } else {
            logger.debug("Output file selection cancelled");
            return null;
        }
    }
    
    /**
     * Analyzes an Excel file asynchronously
     * 
     * @param file The file to analyze
     * @return CompletableFuture with analysis result
     */
    public CompletableFuture<FileAnalysisService.AnalysisResult> analyzeFileAsync(File file) {
        logger.debug("Starting asynchronous file analysis for: {}", file.getName());
        
        // Validate file first
        FileAnalysisService.FileValidationResult validation = fileAnalysisService.validateFile(file);
        if (!validation.isValid()) {
            logger.warn("File validation failed: {}", validation.getErrorMessage());
            return CompletableFuture.completedFuture(
                FileAnalysisService.AnalysisResult.error(validation.getErrorMessage()));
        }
        
        return fileAnalysisService.analyzeFileAsync(file);
    }
    
    /**
     * Validates if a file is a supported Excel format
     * 
     * @param file The file to validate
     * @return Validation result with success status and error message if applicable
     */
    public FileAnalysisService.FileValidationResult validateFile(File file) {
        return fileAnalysisService.validateFile(file);
    }
    
    /**
     * Sets up callbacks for file analysis operations
     * 
     * @param logCallback Callback for log messages
     * @param progressCallback Callback for progress updates
     */
    public void setCallbacks(java.util.function.Consumer<String> logCallback,
                           java.util.function.Consumer<FileAnalysisService.AnalysisProgress> progressCallback) {
        fileAnalysisService.setLogCallback(logCallback);
        fileAnalysisService.setProgressCallback(progressCallback);
    }
    
    // Private helper methods
    
    private FileChooser createExcelFileChooser() {
        FileChooser fileChooser = new FileChooser();
        
        // Add Excel file filters
        FileChooser.ExtensionFilter excelFilter = new FileChooser.ExtensionFilter(
            "Excel Files", "*.xlsx", "*.xls");
        FileChooser.ExtensionFilter xlsxFilter = new FileChooser.ExtensionFilter(
            "Excel 2007+ Files (*.xlsx)", "*.xlsx");
        FileChooser.ExtensionFilter xlsFilter = new FileChooser.ExtensionFilter(
            "Excel 97-2003 Files (*.xls)", "*.xls");
        
        fileChooser.getExtensionFilters().addAll(excelFilter, xlsxFilter, xlsFilter);
        fileChooser.setSelectedExtensionFilter(excelFilter);
        
        return fileChooser;
    }
    
    private String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(lastDot) : "";
    }
    
    /**
     * Gets the maximum allowed file size in MB
     * 
     * @return Maximum file size in MB
     */
    public int getMaxFileSizeMB() {
        return configManager.getMaxFileSizeMB();
    }
    
    /**
     * Checks if a file size is within limits
     * 
     * @param file The file to check
     * @return true if file size is acceptable, false otherwise
     */
    public boolean isFileSizeAcceptable(File file) {
        long maxSizeBytes = (long) getMaxFileSizeMB() * 1024 * 1024;
        return file.length() <= maxSizeBytes;
    }
}
