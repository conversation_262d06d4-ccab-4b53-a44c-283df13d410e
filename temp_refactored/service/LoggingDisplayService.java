package com.planb.exceltranslator.ui.service;

import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.scene.control.TextArea;
import javafx.util.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Service responsible for managing logging display in the UI.
 * Handles log message formatting, auto-scrolling, and log level categorization.
 */
public class LoggingDisplayService {
    
    private static final Logger logger = LoggerFactory.getLogger(LoggingDisplayService.class);
    private static final int MAX_LOG_LINES = 500;
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    
    private final TextArea logArea;
    private boolean userIsScrollingManually = false;
    private Timeline autoScrollResetTimer;
    
    public LoggingDisplayService(TextArea logArea) {
        this.logArea = logArea;
        logger.debug("LoggingDisplayService created");
    }
    
    /**
     * Initializes the logging display with proper settings
     */
    public void initialize() {
        if (logArea == null) {
            logger.error("logArea TextArea is null - FXML injection failed");
            return;
        }
        
        setupLogArea();
        setupScrollBehavior();
        
        logInfo("========== APPLICATION INITIALIZED ==========");
        logInfo("Excel Translator ready - Select a file to begin");
        
        logger.debug("LoggingDisplayService initialized");
    }
    
    /**
     * Logs an informational message
     * 
     * @param message The message to log
     */
    public void logInfo(String message) {
        addLogMessage(message, LogLevel.INFO);
    }
    
    /**
     * Logs a warning message
     * 
     * @param message The message to log
     */
    public void logWarning(String message) {
        addLogMessage(message, LogLevel.WARNING);
    }
    
    /**
     * Logs an error message
     * 
     * @param message The message to log
     */
    public void logError(String message) {
        addLogMessage(message, LogLevel.ERROR);
    }
    
    /**
     * Logs a debug message (only shown if debug mode is enabled)
     * 
     * @param message The message to log
     */
    public void logDebug(String message) {
        // Only log debug messages if debug mode is enabled
        // This could be controlled by configuration
        addLogMessage(message, LogLevel.DEBUG);
    }
    
    /**
     * Adds a log message with default INFO level
     * 
     * @param message The message to log
     */
    public void addLogMessage(String message) {
        addLogMessage(message, LogLevel.INFO);
    }
    
    /**
     * Adds a log message with specified level
     * 
     * @param message The message to log
     * @param level The log level
     */
    public void addLogMessage(String message, LogLevel level) {
        if (message == null || message.trim().isEmpty()) {
            return;
        }
        
        Platform.runLater(() -> {
            String timestamp = LocalDateTime.now().format(TIME_FORMATTER);
            String formattedMessage = String.format("[%s] %s %s", 
                timestamp, level.getPrefix(), message.trim());
            
            appendToLogArea(formattedMessage);
            autoScrollToBottom();
        });
        
        // Also log to SLF4J logger based on level
        switch (level) {
            case ERROR:
                logger.error("UI Log: {}", message);
                break;
            case WARNING:
                logger.warn("UI Log: {}", message);
                break;
            case DEBUG:
                logger.debug("UI Log: {}", message);
                break;
            case INFO:
            default:
                logger.info("UI Log: {}", message);
                break;
        }
    }
    
    /**
     * Clears all log messages
     */
    public void clearLogs() {
        Platform.runLater(() -> {
            if (logArea != null) {
                logArea.clear();
                logInfo("Logs cleared");
            }
        });
        
        logger.debug("Log area cleared");
    }
    
    /**
     * Exports current logs to a string
     * 
     * @return Current log content
     */
    public String exportLogs() {
        return logArea != null ? logArea.getText() : "";
    }
    
    /**
     * Sets up the log area with proper configuration
     */
    private void setupLogArea() {
        logArea.clear();
        logArea.setWrapText(true);
        logArea.setEditable(false);
        logArea.setFocusTraversable(true);
        
        // Set up keyboard navigation
        logArea.setOnKeyPressed(event -> {
            switch (event.getCode()) {
                case UP:
                case DOWN:
                case PAGE_UP:
                case PAGE_DOWN:
                case HOME:
                case END:
                    userIsScrollingManually = true;
                    resetAutoScrollTimer();
                    break;
                default:
                    break;
            }
        });
    }
    
    /**
     * Sets up intelligent auto-scroll behavior
     */
    private void setupScrollBehavior() {
        logArea.setOnMouseClicked(event -> {
            userIsScrollingManually = true;
            resetAutoScrollTimer();
        });
        
        logArea.setOnScroll(event -> {
            userIsScrollingManually = true;
            resetAutoScrollTimer();
        });
    }
    
    /**
     * Appends a message to the log area with line limit enforcement
     */
    private void appendToLogArea(String formattedMessage) {
        String currentText = logArea.getText();
        String newText = currentText + formattedMessage + "\n";
        
        // Enforce line limit
        String[] lines = newText.split("\n");
        if (lines.length > MAX_LOG_LINES) {
            int linesToKeep = MAX_LOG_LINES;
            StringBuilder trimmedText = new StringBuilder();
            for (int i = lines.length - linesToKeep; i < lines.length; i++) {
                trimmedText.append(lines[i]).append("\n");
            }
            newText = trimmedText.toString();
        }
        
        logArea.setText(newText);
    }
    
    /**
     * Automatically scrolls to bottom if user is not manually scrolling
     */
    private void autoScrollToBottom() {
        if (!userIsScrollingManually && logArea != null) {
            Platform.runLater(() -> {
                Platform.runLater(() -> {
                    if (!userIsScrollingManually && logArea != null) {
                        logArea.setScrollTop(Double.MAX_VALUE);
                    }
                });
            });
        }
    }
    
    /**
     * Resets the auto-scroll timer to resume auto-scrolling after user inactivity
     */
    private void resetAutoScrollTimer() {
        if (autoScrollResetTimer != null) {
            autoScrollResetTimer.stop();
        }
        
        autoScrollResetTimer = new Timeline(new KeyFrame(Duration.seconds(3), event -> {
            userIsScrollingManually = false;
            Platform.runLater(() -> {
                if (!userIsScrollingManually && logArea != null) {
                    logArea.setScrollTop(Double.MAX_VALUE);
                }
            });
        }));
        autoScrollResetTimer.play();
    }
    
    /**
     * Gets the current number of log lines
     * 
     * @return Number of lines in the log area
     */
    public int getLogLineCount() {
        return logArea != null ? logArea.getText().split("\n").length : 0;
    }
    
    /**
     * Checks if the log area is at the bottom
     * 
     * @return true if scrolled to bottom, false otherwise
     */
    public boolean isAtBottom() {
        if (logArea == null) return true;
        
        // This is a simplified check - in a real implementation you might
        // want to check the actual scroll position
        return !userIsScrollingManually;
    }
    
    /**
     * Log levels for proper categorization
     */
    public enum LogLevel {
        INFO("INFO:", "#FFFFFF"),
        WARNING("WARNING:", "#FFD93D"),
        ERROR("ERROR:", "#FF6B6B"),
        DEBUG("DEBUG:", "#CCCCCC");
        
        private final String prefix;
        private final String color;
        
        LogLevel(String prefix, String color) {
            this.prefix = prefix;
            this.color = color;
        }
        
        public String getPrefix() {
            return prefix;
        }
        
        public String getColor() {
            return color;
        }
    }
}
