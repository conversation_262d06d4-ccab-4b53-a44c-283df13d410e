package com.planb.exceltranslator.ui.service;

import javafx.application.Platform;
import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;
import javafx.scene.control.TextArea;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

/**
 * Service responsible for managing dialog interactions.
 * Handles error dialogs, information dialogs, confirmations, and application dialogs.
 */
public class DialogService {
    
    private static final Logger logger = LoggerFactory.getLogger(DialogService.class);
    
    public DialogService() {
        logger.debug("DialogService created");
    }
    
    /**
     * Shows an error dialog with the specified title and message
     * 
     * @param title Dialog title
     * @param message Error message
     */
    public void showError(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = createAlert(Alert.AlertType.ERROR, title, message);
            alert.showAndWait();
        });
        
        logger.error("Error dialog shown - {}: {}", title, message);
    }
    
    /**
     * Shows an error dialog with title, message, and detailed error information
     * 
     * @param title Dialog title
     * @param message Error message
     * @param details Detailed error information
     */
    public void showError(String title, String message, String details) {
        Platform.runLater(() -> {
            Alert alert = createAlert(Alert.AlertType.ERROR, title, message);
            
            if (details != null && !details.trim().isEmpty()) {
                TextArea textArea = new TextArea(details);
                textArea.setEditable(false);
                textArea.setWrapText(true);
                textArea.setMaxWidth(Double.MAX_VALUE);
                textArea.setMaxHeight(Double.MAX_VALUE);
                
                alert.getDialogPane().setExpandableContent(textArea);
            }
            
            alert.showAndWait();
        });
        
        logger.error("Error dialog with details shown - {}: {} | Details: {}", title, message, details);
    }
    
    /**
     * Shows an information dialog with the specified title and message
     * 
     * @param title Dialog title
     * @param message Information message
     */
    public void showInfo(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = createAlert(Alert.AlertType.INFORMATION, title, message);
            alert.showAndWait();
        });
        
        logger.info("Info dialog shown - {}: {}", title, message);
    }
    
    /**
     * Shows a warning dialog with the specified title and message
     * 
     * @param title Dialog title
     * @param message Warning message
     */
    public void showWarning(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = createAlert(Alert.AlertType.WARNING, title, message);
            alert.showAndWait();
        });
        
        logger.warn("Warning dialog shown - {}: {}", title, message);
    }
    
    /**
     * Shows a confirmation dialog and returns the user's choice
     * 
     * @param title Dialog title
     * @param message Confirmation message
     * @return true if user confirmed (OK/Yes), false if cancelled (Cancel/No)
     */
    public boolean showConfirmation(String title, String message) {
        Alert alert = createAlert(Alert.AlertType.CONFIRMATION, title, message);
        Optional<ButtonType> result = alert.showAndWait();
        
        boolean confirmed = result.isPresent() && result.get() == ButtonType.OK;
        logger.debug("Confirmation dialog shown - {}: {} | Result: {}", title, message, confirmed);
        
        return confirmed;
    }
    
    /**
     * Shows a confirmation dialog with custom button types
     * 
     * @param title Dialog title
     * @param message Confirmation message
     * @param buttons Custom button types
     * @return Selected button type or null if dialog was closed
     */
    public ButtonType showConfirmation(String title, String message, ButtonType... buttons) {
        Alert alert = createAlert(Alert.AlertType.CONFIRMATION, title, message);
        alert.getButtonTypes().setAll(buttons);
        
        Optional<ButtonType> result = alert.showAndWait();
        ButtonType selected = result.orElse(null);
        
        logger.debug("Custom confirmation dialog shown - {}: {} | Result: {}", title, message, selected);
        return selected;
    }
    
    /**
     * Opens the preferences dialog
     */
    public void openPreferences() {
        logger.debug("Opening preferences dialog");
        
        try {
            // This would typically load the preferences FXML and create a new window
            // For now, show a placeholder
            showInfo("Preferences", "Preferences dialog would open here.\nThis feature is under development.");
            
        } catch (Exception e) {
            logger.error("Failed to open preferences dialog", e);
            showError("Error", "Failed to open preferences dialog", e.getMessage());
        }
    }
    
    /**
     * Shows the about dialog
     */
    public void showAbout() {
        logger.debug("Showing about dialog");
        
        String aboutMessage = buildAboutMessage();
        showInfo("About Excel Translator", aboutMessage);
    }
    
    /**
     * Shows a confirmation dialog for application exit
     * 
     * @return true if user confirmed exit, false if cancelled
     */
    public boolean confirmExit() {
        logger.debug("Showing exit confirmation");
        
        boolean confirmed = showConfirmation(
            "Exit Application", 
            "Are you sure you want to exit Excel Translator?\n\nAny ongoing translation will be cancelled."
        );
        
        if (confirmed) {
            logger.info("User confirmed application exit");
            Platform.exit();
        } else {
            logger.debug("User cancelled application exit");
        }
        
        return confirmed;
    }
    
    /**
     * Shows a dialog for cancelling ongoing operations
     * 
     * @param operationName Name of the operation to cancel
     * @return true if user confirmed cancellation, false otherwise
     */
    public boolean confirmCancellation(String operationName) {
        return showConfirmation(
            "Cancel " + operationName,
            "Are you sure you want to cancel the " + operationName.toLowerCase() + "?\n\nProgress will be lost."
        );
    }
    
    /**
     * Shows a dialog asking user to select between options
     * 
     * @param title Dialog title
     * @param message Dialog message
     * @param options Available options
     * @return Selected option or null if cancelled
     */
    public String showOptionDialog(String title, String message, String... options) {
        if (options == null || options.length == 0) {
            return null;
        }
        
        Alert alert = createAlert(Alert.AlertType.CONFIRMATION, title, message);
        
        // Create button types for each option
        ButtonType[] buttonTypes = new ButtonType[options.length + 1];
        for (int i = 0; i < options.length; i++) {
            buttonTypes[i] = new ButtonType(options[i]);
        }
        buttonTypes[options.length] = ButtonType.CANCEL;
        
        alert.getButtonTypes().setAll(buttonTypes);
        
        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() != ButtonType.CANCEL) {
            return result.get().getText();
        }
        
        return null;
    }
    
    // Private helper methods
    
    /**
     * Creates a basic alert dialog with common settings
     */
    private Alert createAlert(Alert.AlertType alertType, String title, String message) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        
        // Set application icon if available
        Stage stage = (Stage) alert.getDialogPane().getScene().getWindow();
        // stage.getIcons().add(new Image("icon.png")); // Uncomment if icon is available
        
        return alert;
    }
    
    /**
     * Builds the about message with application information
     */
    private String buildAboutMessage() {
        return """
            Excel Translator - PLAN-B
            Version 1.0.0
            
            A powerful tool for translating Excel files using multiple translation services.
            
            Features:
            • Support for .xlsx and .xls files
            • Multiple translation providers (DeepL, Google Translate)
            • Batch processing for efficient translation
            • Language auto-detection
            • Progress tracking and cancellation
            
            Developed with JavaFX and Apache POI
            
            © 2024 PLAN-B Team
            """;
    }
}
