package com.planb.exceltranslator.ui.controller;

import com.planb.exceltranslator.application.*;
import com.planb.exceltranslator.domain.model.*;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import com.planb.exceltranslator.ui.service.*;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.Pane;
import javafx.scene.shape.Circle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;

/**
 * Main Dashboard Controller for the Excel Translator application.
 * Coordinates between UI components and business services.
 * Delegates specific functionality to specialized service classes.
 */
public class DashboardController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(DashboardController.class);
    
    // FXML UI Components - File Section
    @FXML private Pane fileDragAndDrop;
    @FXML private Label selectedFile;
    @FXML private Button selectFileButton;
    @FXML private Label fileSize;
    
    // FXML UI Components - Translation Settings
    @FXML private ComboBox<Language> sourceLanguage;
    @FXML private ComboBox<Language> targetLanguage;
    @FXML private ComboBox<TranslatorType> translator;
    @FXML private Spinner<Integer> batchSize;
    
    // FXML UI Components - Progress and Status
    @FXML private ProgressBar progressBar;
    @FXML private Label progressStatus;
    @FXML private Label processCompletionStatus;
    @FXML private Circle statusIndicator;
    
    // FXML UI Components - Sheets and Logs
    @FXML private AnchorPane sheetsDetectedList;
    @FXML private ScrollPane sheetsScrollPane;
    @FXML private TextArea logArea;
    
    // FXML UI Components - Action Buttons
    @FXML private Button translateButton;
    @FXML private Button cancelButton;
    @FXML private Button exportButton;
    
    // FXML UI Components - Menu Items
    @FXML private MenuItem selectFileMenuItem;
    @FXML private MenuItem exportFileMenuItem;
    @FXML private MenuItem preferencesMenuItem;
    @FXML private MenuItem aboutMenuItem;
    @FXML private MenuItem exitMenuItem;
    
    // Core Services
    private ConfigurationManager configManager;
    private LanguageManager languageManager;
    
    // UI Service Layer
    private UIInitializationService uiInitializationService;
    private FileSelectionService fileSelectionService;
    private TranslationControlService translationControlService;
    private ProgressTrackingService progressTrackingService;
    private LoggingDisplayService loggingDisplayService;
    private SheetsDisplayService sheetsDisplayService;
    private LanguageBindingService languageBindingService;
    private DialogService dialogService;
    
    // Application State
    private File selectedExcelFile;
    private List<ExcelSheet> detectedSheets;
    private TranslationResult lastTranslationResult;
    private Language autoDetectedLanguage;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.info("Initializing Dashboard Controller...");
        
        try {
            initializeServices();
            initializeUI();
            setupEventHandlers();
            updateUIState();
            
            logger.info("Dashboard Controller initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize Dashboard Controller", e);
            showError("Initialization Error", "Failed to initialize the dashboard: " + e.getMessage());
        }
    }
    
    /**
     * Initializes all core services
     */
    private void initializeServices() {
        configManager = ConfigurationManager.getInstance();
        languageManager = LanguageManager.getInstance();
        
        // Initialize service layer
        uiInitializationService = new UIInitializationService(configManager);
        fileSelectionService = new FileSelectionService(configManager);
        translationControlService = new TranslationControlService(configManager);
        progressTrackingService = new ProgressTrackingService();
        loggingDisplayService = new LoggingDisplayService(logArea);
        sheetsDisplayService = new SheetsDisplayService();
        languageBindingService = new LanguageBindingService(languageManager);
        dialogService = new DialogService();
        
        logger.debug("Services initialized successfully");
    }
    
    /**
     * Initializes UI components
     */
    private void initializeUI() {
        uiInitializationService.initializeComboBoxes(sourceLanguage, targetLanguage, translator);
        uiInitializationService.initializeSpinner(batchSize);
        uiInitializationService.initializeProgressComponents(progressBar, progressStatus, processCompletionStatus);
        uiInitializationService.initializeDragAndDrop(fileDragAndDrop, this::onFileDropped);
        uiInitializationService.initializeScrollPane(sheetsScrollPane);
        
        loggingDisplayService.initialize();
        progressTrackingService.initialize(progressBar, progressStatus, processCompletionStatus, statusIndicator);
        
        logger.debug("UI components initialized successfully");
    }
    
    /**
     * Sets up event handlers for UI components
     */
    private void setupEventHandlers() {
        // File operations
        selectFileButton.setOnAction(e -> selectFile());
        selectFileMenuItem.setOnAction(e -> selectFile());
        
        // Translation operations
        translateButton.setOnAction(e -> startTranslation());
        cancelButton.setOnAction(e -> cancelTranslation());
        exportButton.setOnAction(e -> exportTranslatedFile());
        exportFileMenuItem.setOnAction(e -> exportTranslatedFile());
        
        // Menu operations
        preferencesMenuItem.setOnAction(e -> openPreferences());
        aboutMenuItem.setOnAction(e -> showAbout());
        exitMenuItem.setOnAction(e -> exitApplication());
        
        // Language and settings change handlers
        languageBindingService.setupLanguageBindings(sourceLanguage, targetLanguage, this::onLanguageChange);
        translator.valueProperty().addListener((obs, oldVal, newVal) -> onTranslatorChange(newVal));
        batchSize.valueProperty().addListener((obs, oldVal, newVal) -> onBatchSizeChange(newVal));
        
        logger.debug("Event handlers set up successfully");
    }
    
    /**
     * Updates the UI state based on current application state
     */
    private void updateUIState() {
        boolean hasFile = selectedExcelFile != null;
        boolean hasSheets = detectedSheets != null && !detectedSheets.isEmpty();
        boolean hasTranslationResult = lastTranslationResult != null;
        
        translateButton.setDisable(!hasSheets || translationControlService.isTranslationInProgress());
        cancelButton.setDisable(!translationControlService.isTranslationInProgress());
        exportButton.setDisable(!hasTranslationResult);
        exportFileMenuItem.setDisable(!hasTranslationResult);
        
        if (hasFile) {
            selectedFile.setText(selectedExcelFile.getName());
            fileSize.setText(formatFileSize(selectedExcelFile.length()));
        } else {
            selectedFile.setText("No file selected");
            fileSize.setText("");
        }
        
        logger.debug("UI state updated");
    }
    
    // Event Handler Methods
    
    private void selectFile() {
        logger.debug("File selection requested");
        File file = fileSelectionService.selectExcelFile();
        if (file != null) {
            handleFileSelection(file);
        }
    }
    
    private void onFileDropped(File file) {
        logger.debug("File dropped: {}", file.getName());
        handleFileSelection(file);
    }
    
    private void handleFileSelection(File file) {
        selectedExcelFile = file;
        loggingDisplayService.logInfo("File selected: " + file.getName());
        
        // Delegate to file analysis service
        fileSelectionService.analyzeFileAsync(file)
            .thenAccept(result -> {
                if (result.isSuccess()) {
                    handleAnalysisSuccess(result);
                } else {
                    handleAnalysisError(result.getErrorMessage());
                }
            })
            .exceptionally(throwable -> {
                handleAnalysisError(throwable.getMessage());
                return null;
            });
        
        updateUIState();
    }
    
    private void handleAnalysisSuccess(FileAnalysisService.AnalysisResult result) {
        detectedSheets = result.getSheets();
        sheetsDisplayService.displaySheets(sheetsDetectedList, detectedSheets);
        
        // Auto-detect language if enabled
        languageBindingService.detectLanguageAsync(detectedSheets, sourceLanguage.getValue())
            .thenAccept(detectedLang -> {
                if (detectedLang != null) {
                    autoDetectedLanguage = detectedLang;
                    sourceLanguage.setValue(detectedLang);
                    loggingDisplayService.logInfo("Auto-detected language: " + detectedLang.getDisplayName());
                }
            });
        
        updateUIState();
    }
    
    private void handleAnalysisError(String errorMessage) {
        loggingDisplayService.logError("File analysis failed: " + errorMessage);
        showError("Analysis Error", errorMessage);
        updateUIState();
    }
    
    private void startTranslation() {
        if (selectedExcelFile == null || detectedSheets == null) {
            showError("Translation Error", "Please select a valid Excel file first.");
            return;
        }
        
        Language source = sourceLanguage.getValue();
        Language target = targetLanguage.getValue();
        TranslatorType translatorType = translator.getValue();
        int batchSizeValue = batchSize.getValue();
        
        if (source == null || target == null || translatorType == null) {
            showError("Translation Error", "Please select source language, target language, and translator.");
            return;
        }
        
        loggingDisplayService.logInfo("Starting translation...");
        
        TranslationRequest request = new TranslationRequest.Builder()
            .file(selectedExcelFile)
            .sheets(detectedSheets)
            .sourceLanguage(source)
            .targetLanguage(target)
            .translatorType(translatorType)
            .batchSize(batchSizeValue)
            .build();
        
        translationControlService.startTranslationAsync(request,
            progressTrackingService::updateProgress,
            loggingDisplayService::logInfo)
            .thenAccept(result -> {
                lastTranslationResult = result;
                loggingDisplayService.logInfo("Translation completed successfully!");
                updateUIState();
            })
            .exceptionally(throwable -> {
                loggingDisplayService.logError("Translation failed: " + throwable.getMessage());
                showError("Translation Error", throwable.getMessage());
                updateUIState();
                return null;
            });
        
        updateUIState();
    }
    
    private void cancelTranslation() {
        translationControlService.cancelTranslation();
        loggingDisplayService.logInfo("Translation cancelled");
        updateUIState();
    }
    
    private void exportTranslatedFile() {
        if (lastTranslationResult == null) {
            showError("Export Error", "No translation result available for export.");
            return;
        }
        
        File outputFile = fileSelectionService.selectOutputFile(selectedExcelFile);
        if (outputFile != null) {
            translationControlService.exportTranslatedFileAsync(lastTranslationResult, outputFile)
                .thenRun(() -> {
                    loggingDisplayService.logInfo("File exported successfully: " + outputFile.getName());
                    showInfo("Export Success", "File exported successfully to: " + outputFile.getAbsolutePath());
                })
                .exceptionally(throwable -> {
                    loggingDisplayService.logError("Export failed: " + throwable.getMessage());
                    showError("Export Error", throwable.getMessage());
                    return null;
                });
        }
    }
    
    // Language and Settings Change Handlers
    
    private void onLanguageChange(Language source, Language target) {
        logger.debug("Language changed - Source: {}, Target: {}", 
            source != null ? source.getDisplayName() : "null",
            target != null ? target.getDisplayName() : "null");
        
        if (source != null && target != null && source.equals(target)) {
            // Auto-suggest alternative target language
            Language suggested = languageBindingService.suggestAlternativeTarget(source);
            if (suggested != null) {
                targetLanguage.setValue(suggested);
                loggingDisplayService.logInfo("Auto-suggested target language: " + suggested.getDisplayName());
            }
        }
    }
    
    private void onTranslatorChange(TranslatorType newTranslator) {
        if (newTranslator != null) {
            loggingDisplayService.logInfo("Translator changed to: " + newTranslator.getDisplayName());
        }
    }
    
    private void onBatchSizeChange(Integer newBatchSize) {
        if (newBatchSize != null) {
            loggingDisplayService.logInfo("Batch size changed to: " + newBatchSize);
        }
    }
    
    // Menu Operations
    
    private void openPreferences() {
        dialogService.openPreferences();
    }
    
    private void showAbout() {
        dialogService.showAbout();
    }
    
    private void exitApplication() {
        dialogService.confirmExit();
    }
    
    // Utility Methods
    
    private void showError(String title, String message) {
        dialogService.showError(title, message);
    }
    
    private void showInfo(String title, String message) {
        dialogService.showInfo(title, message);
    }
    
    private String formatFileSize(long sizeInBytes) {
        if (sizeInBytes < 1024) {
            return sizeInBytes + " B";
        } else if (sizeInBytes < 1024 * 1024) {
            return String.format("%.1f KB", sizeInBytes / 1024.0);
        } else {
            return String.format("%.1f MB", sizeInBytes / (1024.0 * 1024.0));
        }
    }
}
